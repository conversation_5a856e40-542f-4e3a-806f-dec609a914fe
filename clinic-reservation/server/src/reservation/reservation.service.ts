import { forwardRef, HttpStatus, Inject, Injectable } from '@nestjs/common';
import * as Sentry from '@sentry/nestjs';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { GraphQLError } from 'graphql';
import {
  ModelClass,
  PartialModelGraph,
  PartialModelObject,
  Transaction,
} from 'objection';

import {
  HOSPITAL_KARTE_STATUS,
  MODEL_DEFAULT_VALUE,
  RESERVER_REQUEST_TYPE,
  TEMPLATE_MAIL_CODE,
  TEMPLATE_MAIL_KEY,
} from '@/common/constants';
import {
  DESIRED_DATE_STATUS,
  DESIRED_TYPE,
  GENDER_TYPE,
  IS_DELETED,
  MEETING_STATUS,
  PAYMENT_ACTION_TYPE,
  PAYMENT_STATUS,
  PAYMENT_TYPE,
  PHARMACY_PRESCRIPTION_RECEIVE_METHOD,
  PHARMACY_RESERVE_DETAIL_STATUS,
  PHARMACY_STATUS_CANCEL_TYPE,
  RAINIIN_STATUS,
  RESERVATION_METHOD,
  RESERVATION_STATUS,
  RESERVATION_TYPE,
  TREATMENT_TYPE,
} from '@/common/constants/master-type';
import { Logger } from '@/common/logger/logger';
import { MailService } from '@/common/service/mail.service';
import { formatZipCode } from '@/common/utils';
import { getDayjsDateJST } from '@/common/utils/date-time';
import { ClinicError } from '@/common/utils/error';
import { getPaymentMethod } from '@/common/utils/payment';
import { getPrescriptionReceiveMethod } from '@/common/utils/pharmacy';
import { formatPhoneNumber } from '@/common/utils/phone-number';
import {
  convertExamTimePeriod,
  getCalendarName,
  handleReservationError,
} from '@/common/utils/reservation';
import { execWithTx } from '@/common/utils/transaction';
import { ExamTimeSlotService } from '@/exam-time-slot/exam-time-slot.service';
import { BaseModel } from '@/models/BaseModel';
import { Customer } from '@/models/Customer';
import { CustomerDeliveryAddress } from '@/models/CustomerDeliveryAddress';
import { ExamTimeSlot } from '@/models/ExamTimeSlot';
import { HospitalInfo } from '@/models/HospitalInfo';
import { Meeting } from '@/models/Meeting';
import { Patient } from '@/models/Patient';
import { PaymentClinicDetail } from '@/models/PaymentClinicDetail';
import { PaymentPharmacyDetail } from '@/models/PaymentPharmacyDetail';
import { PharmacyDesiredDate } from '@/models/PharmacyDesiredDate';
import { PharmacyReserve } from '@/models/PharmacyReserve';
import { PharmacyReserveDetail } from '@/models/PharmacyReserveDetail';
import { PharmacyReserveStatusHistory } from '@/models/PharmacyReserveStatusHistory';
import { PortalCustomerPharmacy } from '@/models/PortalCustomerPharmacy';
import { PortalHospital } from '@/models/PortalHospital';
import { Reservation } from '@/models/Reservation';
import { ReservationDetail } from '@/models/ReservationDetail';
import { ReservationDetailHistory } from '@/models/ReservationDetailHistory';
import { PaymentDetail } from '@/payment/payment.output';
import PharmacyService from '@/pharmacy/pharmacy.service';
import { PharmacyDeliveryAddressService } from '@/pharmacy-delivery-address/pharmacy-delivery-address.service';
import {
  BookingFromCalendarOrPortalInput,
  UpdateBookingInfoInput,
  UpdateReceptionStaticCellRequestInput,
} from '@/raiin/raiin.input';
import RaiinService from '@/raiin/raiin.service';
import { SearchHospitalService } from '@/search-hospital/search-hospital.service';

import { ERROR_TYPE } from '../common/constants/error-type';
import { LoginCustomer } from '../customer/customer.output';
import { CustomerService } from '../customer/customer.service';
import { PatientService } from '../patient/patient.service';
import { SurveyService } from '../survey/survey.service';
import {
  CustomersTreatmentHistoryInput,
  CustomerTreatmentInput,
  ReserveInput,
  UpdateReserveInput,
} from './reservation.input';
import {
  CustomerTreatmentType,
  RaiinInfoError,
  ReservationDetailError,
  ReservationDetailResponse,
  ReservationInfo,
  ReservationPharmacyInfo,
  ReservationResponse,
  ReserveCancelResponse,
} from './reservation.output';

dayjs.extend(isBetween);
dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export default class ReservationService {
  constructor(
    @Inject(Reservation.name)
    private reservation: ModelClass<Reservation>,
    @Inject(ReservationDetail.name)
    private reservationDetail: ModelClass<ReservationDetail>,
    @Inject(PharmacyReserve.name)
    private pharmacyReserve: ModelClass<PharmacyReserve>,
    @Inject(PharmacyReserveDetail.name)
    private pharmacyReserveDetail: ModelClass<PharmacyReserveDetail>,
    @Inject(PortalCustomerPharmacy.name)
    private portalCustomerPharmacy: ModelClass<PortalCustomerPharmacy>,
    @Inject(ReservationDetailHistory.name)
    private reservationDetailHistory: ModelClass<ReservationDetailHistory>,
    @Inject(PharmacyReserveStatusHistory.name)
    private pharmacyReserveStatusHistory: ModelClass<PharmacyReserveStatusHistory>,
    @Inject(Meeting.name)
    private meeting: ModelClass<Meeting>,
    @Inject(HospitalInfo.name)
    private hospital: ModelClass<HospitalInfo>,
    @Inject(PharmacyDesiredDate.name)
    private pharmacyDesiredDate: ModelClass<PharmacyDesiredDate>,
    @Inject(Patient.name)
    private patient: ModelClass<Patient>,
    @Inject(PaymentClinicDetail.name)
    private paymentClinicDetail: ModelClass<PaymentClinicDetail>,
    @Inject(PaymentPharmacyDetail.name)
    private paymentPharmacyDetail: ModelClass<PaymentPharmacyDetail>,
    @Inject(CustomerDeliveryAddress.name)
    private customerDeliveryAddress: ModelClass<CustomerDeliveryAddress>,
    @Inject(Customer.name)
    private customer: ModelClass<Customer>,
    @Inject(forwardRef(() => SurveyService))
    private readonly surveyService: SurveyService,
    @Inject(forwardRef(() => PharmacyService))
    private readonly pharmacyService: PharmacyService,
    private readonly mailService: MailService,
    private readonly examSlotService: ExamTimeSlotService,
    private readonly openSearchService: SearchHospitalService,
    private readonly pharmacyDeliveryAddressService: PharmacyDeliveryAddressService,
    private readonly patientService: PatientService,
    private readonly customerService: CustomerService,
    private readonly logger: Logger,
    private readonly raiinService: RaiinService,
  ) {}

  async reserve(
    params: ReserveInput,
    loginCustomer: LoginCustomer,
  ): Promise<Reservation> {
    const hospitalInfo = await this.hospital
      .query()
      .joinRelated('portalHospital')
      .where('portalHospital.hospitalId', params.hospitalId)
      .andWhere('hpInf.isDeleted', IS_DELETED.FALSE)
      .andWhere('portalHospital.isDeleted', IS_DELETED.FALSE)
      .withGraphFetched('finCodeInfo')
      .first()
      .throwIfNotFound();

    const loginPatient = await this.getPatientByPortalCustomer(
      loginCustomer,
      hospitalInfo.hpId,
    );
    const examTimeSlot = await this.examSlotService.getExamSlotById(
      params.examTimeSlotId,
    );

    const reservationDetails = await Promise.all(
      params.customers.map((customer) =>
        this.getReservationDetail(
          customer,
          examTimeSlot,
          loginCustomer,
          hospitalInfo,
          params,
        ),
      ),
    );
    const pharmacyData = await this.findHospitalPharmacy();

    const { pharmacyReserve, portalCustomerPharmacy } =
      await this.getPharmacyReserveInfo(
        loginCustomer,
        pharmacyData.hpId,
        params,
      );

    const meeting =
      params.reserveType === RESERVATION_TYPE.ONLINE
        ? {
            patientId: loginPatient.ptId,
            hospitalId: hospitalInfo.hpId,
            status: MEETING_STATUS.RESERVED,
          }
        : undefined;

    const trx = await Reservation.startTransaction();
    try {
      const reservation = await this.reservation
        .query(trx)
        .insertGraphAndFetch(
          {
            ...MODEL_DEFAULT_VALUE,
            patientId: loginPatient.ptId,
            reservationDetails,
            prescriptionReceiveMethod: params.prescriptionReceiveMethod,
            pharmacyReserve,
            portalCustomerPharmacy,
            meeting,
          },
          { allowRefs: true },
        )
        .withGraphFetched({
          reservationDetails: {
            examTimeSlot: {
              calendar: true,
            },
            patient: true,
            calendarTreatment: true,
          },
        });

      if (pharmacyReserve) {
        const reserveDetailIds = reservation.reservationDetails.map(
          ({ reserveDetailId }) => reserveDetailId,
        );

        const currentPharmacyReserveDetail = await this.pharmacyReserveDetail
          .query(trx)
          .upsertGraphAndFetch(
            reservation.pharmacyReserve.pharmacyReservationDetails.map(
              (pharmacyReservationDetail, index) => ({
                ...pharmacyReservationDetail,
                reserveDetailId: reserveDetailIds[index],
              }),
            ),
          );

        await this.pharmacyService.insertPharmacyStatusHistoriesWhenReservePharmacy(
          trx,
          currentPharmacyReserveDetail,
        );
      }

      await trx.commit();

      const updatedExamTimeSlot = await this.examSlotService.getExamSlotById(
        params.examTimeSlotId,
      );

      await this.openSearchService.updateReservableSlots({
        examTimeSlotId: params.examTimeSlotId,
        reservableSlots: updatedExamTimeSlot.reservableSlots,
        hospitalId: updatedExamTimeSlot.calendar.hospitalId,
      });

      const reserveDtlIds = reservation.reservationDetails.map(
        ({ reserveDetailId }) => reserveDetailId,
      );

      // カスタマーに予約完了のお知らせメールを送信
      const templateCode =
        params.reserveType === RESERVATION_TYPE.IN_PERSON
          ? TEMPLATE_MAIL_CODE.NEW_RESERVATION_IN_PERSON
          : TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE;
      await this.sendMailNotification(
        loginCustomer.email,
        reserveDtlIds,
        templateCode,
        true,
      );

      //クリニックに新規予約のお知らせメールを送信
      await this.sendMailNotificationToClinic(
        reserveDtlIds,
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_CLINIC,
      );

      if (
        params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
      ) {
        // 指定住所へ配送が選択されている場合は、薬局24へメールを送付する。
        await this.sendMailNotificationToPharmacy(
          reserveDtlIds,
          TEMPLATE_MAIL_CODE.TO_PHARMACY_FOR_RESERVE,
        );
      }

      // hp_inf.karteStatusがリアル利用中の場合は、raiin_infを登録する。
      if (hospitalInfo.karteStatus === HOSPITAL_KARTE_STATUS.REAL_IN_USE) {
        // Register raiin_inf
        const reserveDetails = reservation.reservationDetails;
        const registerRaiinInfoResList = await Promise.all(
          reserveDetails.map(async (detail) => {
            const registerRaiinInput =
              this.convertReserveDetailToRegisterRaiinInfInput(detail);
            const res = await this.raiinService.registerRaiinInfo(
              registerRaiinInput,
            );
            if (res.errors && res.errors.length > 0) {
              return {
                reserveDetailId: detail.reserveDetailId,
                errors: res.errors,
              };
            }
          }),
        );

        const registerRaiinInfoErrorList: RaiinInfoError[] =
          registerRaiinInfoResList.filter(
            (registerRes) => registerRes != undefined,
          );

        // 予約情報のエラーを処理
        handleReservationError(
          registerRaiinInfoErrorList,
          '予約情報の登録に失敗しました',
        );
      }

      return reservation;
    } catch (error) {
      await trx.rollback();
      throw error;
    }
  }

  async updateReserve(
    params: UpdateReserveInput,
    loginCustomer: LoginCustomer,
  ): Promise<void> {
    const currentReservation = await this.findOne(params.reserveId);
    if (!currentReservation) {
      throw new ClinicError(ERROR_TYPE.NOT_FOUND);
    }

    if (currentReservation.meeting?.isBothJoined) {
      throw new ClinicError(ERROR_TYPE.EDIT_RESERVATION_DEADLINE_PASSED);
    }

    const pharmacyData = await this.findHospitalPharmacy();
    const patient = await this.getPatientByPortalCustomer(
      loginCustomer,
      pharmacyData.hpId,
    );

    const { updatedDetailsNotCanceled } = await execWithTx(async (trx) => {
      let canceledReserveDtlIds: number[] = [];
      const updateRequest = {
        updatedAt: new Date(),
        updatedBy: 'Customer',
      };

      await this.updateReservation(currentReservation, trx, updateRequest);

      const updatedDetails = await this.updateReservationDetails(
        currentReservation.reservationDetails,
        trx,
        {
          ...updateRequest,
          examTimeSlotId: params.examTimeSlotId,
          paymentCardId: params.paymentCardId,
          fincodeCustomerId: params.fincodeCustomerId,
        },
      );

      await this.updatePharmacyAddress(
        params,
        currentReservation,
        patient.ptId,
        pharmacyData.hpId,
        loginCustomer,
        trx,
      );

      if (params.customers) {
        // 受診者の更新
        canceledReserveDtlIds = await this.cancelCustomers(
          loginCustomer,
          params.customers,
          currentReservation,
          trx,
        );
      }

      // キャンセルされていない予約詳細のみを更新する
      const updatedDetailsNotCanceled = updatedDetails.filter(
        (detail) => !canceledReserveDtlIds.includes(detail.reserveDetailId),
      );

      return { updatedDetailsNotCanceled };
    });

    const reserveDtlIds = updatedDetailsNotCanceled.map(
      ({ reserveDetailId }) => reserveDetailId,
    );

    // カスタマーに予約変更のお知らせメールを送信
    if (updatedDetailsNotCanceled.length) {
      const templateCode =
        updatedDetailsNotCanceled[0].reserveType === RESERVATION_TYPE.IN_PERSON
          ? TEMPLATE_MAIL_CODE.UPDATE_RESERVATION_IN_PERSON
          : TEMPLATE_MAIL_CODE.UPDATE_RESERVATION_ONLINE;
      await this.sendMailNotification(
        loginCustomer.email,
        reserveDtlIds,
        templateCode,
        true,
      );
    }

    // クリニックに予約変更のお知らせメールを送信
    await this.sendMailNotificationToClinic(
      reserveDtlIds,
      TEMPLATE_MAIL_CODE.UPDATE_RESERVATION_CLINIC,
    );

    //
    if (
      params.prescriptionReceiveMethod !=
      currentReservation.prescriptionReceiveMethod
    ) {
      if (
        params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
      ) {
        // 薬の受取方法を「指定住所へ配送」以外から「指定住所へ配送」へ変更された場合は、薬局24へメールを送付する。
        await this.sendMailNotificationToPharmacy(
          reserveDtlIds,
          TEMPLATE_MAIL_CODE.TO_PHARMACY_FOR_RESERVE,
        );
      } else if (
        currentReservation.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
      ) {
        // 薬の受取方法を「指定住所へ配送」から「指定住所へ配送」以外へ変更された場合は、薬局24へメールを送付する。
        await this.sendMailNotificationToPharmacy(
          reserveDtlIds,
          TEMPLATE_MAIL_CODE.TO_PHARMACY_FOR_CANCEL_RESERVE,
        );
      }
    }

    // hp_inf.karteStatusがリアル利用中の場合は、raiin_infを更新する。
    const hospitalInfo = currentReservation.patient?.hospital;

    if (hospitalInfo?.karteStatus === HOSPITAL_KARTE_STATUS.REAL_IN_USE) {
      // Update raiin_inf
      const updateRaiinInput = this.convertReserveDetailToUpdateRaiinInfoInput(
        updatedDetailsNotCanceled,
      );
      if (updateRaiinInput) {
        const res = await this.raiinService.updateRaiinInfo(updateRaiinInput);
        if (res.errors && res.errors.length > 0) {
          // 予約情報のエラーを処理
          const updatedRaiinError: RaiinInfoError = {
            reserveDetailId: updateRaiinInput.reserveDetailIds,
            errors: res.errors,
          };
          handleReservationError(
            [updatedRaiinError],
            '予約情報の更新に失敗しました',
          );
        }
      }
    }
  }

  async getReservationDetailsByConditions(
    customerIds: number[],
    type: RESERVER_REQUEST_TYPE,
  ): Promise<ReservationDetail[]> {
    const reserveIds = await this.getReserveIdsByCondition(customerIds, type);

    const queryBuilder = this.reservationDetail
      .query()
      .select(['examTimeSlot.examStartDate as sortDate', 'reserveDetail.*'])
      .joinRelated('[patient.portalCustomer, examTimeSlot]')
      .withGraphFetched({
        patient: {
          hospital: true,
          portalCustomer: true,
        },
        surveyAnswer: true,
        pharmacyReserveDetail: true,
        reservation: {
          meeting: true,
          pharmacyReserve: {
            pharmacyDesiredDates: true,
            pharmacyDeliveryAddress: true,
            patient: {
              hospital: true,
            },
            pharmacyReservationDetails: true,
            meetings: true,
            pharmacyDeliveryHistories: true,
          },
          portalCustomerPharmacy: true,
        },
        examTimeSlot: {
          calendar: true,
        },
        calendarTreatment: {
          treatmentDepartment: {
            treatmentCategory: true,
            hospital: {
              portalHospital: {
                stations: {
                  stationDetail: {
                    railline: true,
                  },
                },
              },
            },
          },
        },
        raiinInf: true,
      })
      .modifyGraph('calendarTreatment', BaseModel.getDeletedRecords)
      .modifyGraph('pharmacyReserveDetail', (builder) => {
        builder
          .where('isDeleted', IS_DELETED.FALSE)
          .whereNot('status', PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED);
      })
      .modifyGraph(
        'reservation.pharmacyReserve.pharmacyDeliveryHistories',
        (builder) => {
          builder.orderBy('createdAt', 'asc');
        },
      )
      .where('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .andWhere('examTimeSlot.isDeleted', IS_DELETED.FALSE)
      .andWhere('patient:portalCustomer.isDeleted', IS_DELETED.FALSE)
      .whereIn('patient:portalCustomer.customerId', customerIds)
      .whereIn('reserveId', reserveIds)
      .orderBy([
        {
          column: 'examTimeSlot.examStartDate',
          order: 'desc',
        },
        {
          column: 'reserveDetailId',
          order: 'asc',
        },
      ]);

    if (type === RESERVER_REQUEST_TYPE.UP_COMING_RESERVE) {
      queryBuilder.whereNot(
        'reserveDetail.status',
        RESERVATION_STATUS.CANCELED,
      );
    }

    // 過去の予約に対して削除された関係も取得
    if (type === RESERVER_REQUEST_TYPE.HISTORY_RESERVE) {
      queryBuilder
        .modifyGraph(
          'calendarTreatment.treatmentDepartment',
          BaseModel.getDeletedRecords,
        )
        .modifyGraph('examTimeSlot', BaseModel.getDeletedRecords)
        .modifyGraph('examTimeSlot.calendar', BaseModel.getDeletedRecords);
    }

    return await queryBuilder;
  }

  async findOne(id: number): Promise<Reservation | undefined> {
    return await this.reservation
      .query()
      .where('isDeleted', IS_DELETED.FALSE)
      .findById(id)
      .withGraphFetched({
        patient: {
          hospital: true,
        },
        pharmacyReserve: {
          pharmacyReservationDetails: true,
          meetings: true,
        },
        portalCustomerPharmacy: true,
        meeting: true,
        reservationDetails: {
          patient: {
            portalCustomer: true,
          },
          reservation: {
            patient: {
              portalCustomer: true,
            },
          },
          examTimeSlot: true,
          surveyAnswer: true,
          examDetails: true,
          calendarTreatment: {
            treatmentDepartment: {
              treatmentCategory: true,
              hospital: {
                portalHospital: {
                  stations: {
                    stationDetail: {
                      railline: true,
                    },
                  },
                },
                finCodeInfo: true,
              },
            },
          },
          raiinInf: true,
        },
      })
      .modifyGraph('pharmacyReserve', (builder) => {
        builder.andWhere('isDeleted', IS_DELETED.FALSE);
      })
      .modifyGraph('portalCustomerPharmacy', (builder) => {
        builder.andWhere('isDeleted', IS_DELETED.FALSE);
      })
      .modifyGraph('reservationDetails', (builder) => {
        builder
          .whereNot('status', RESERVATION_STATUS.CANCELED)
          .andWhere('isDeleted', IS_DELETED.FALSE);
      })
      .modifyGraph('pharmacyReserve.pharmacyReservationDetails', (builder) => {
        builder.modify('isActive');
      });
  }

  async cancelReserve(
    loginCustomer: LoginCustomer,
    reserveId: number,
    cancelReserveList: number[],
    cancelReasonType?: number,
    isNotSendMail?: boolean,
    trx?: Transaction,
  ): Promise<ReserveCancelResponse> {
    const oldReserve = await this.findOne(reserveId);
    if (!oldReserve) {
      throw new ClinicError(ERROR_TYPE.NOT_FOUND);
    }

    const raiinInfs = oldReserve.reservationDetails.map(
      (detail) => detail.raiinInf,
    );

    if (
      raiinInfs.some(
        (raiinInfo) => raiinInfo && raiinInfo.status >= RAINIIN_STATUS.VERIFIED,
      )
    ) {
      throw new ClinicError(ERROR_TYPE.RECEPTION_CONNECTED);
    }

    // ビデオ通話を始めた場合
    if (oldReserve.meeting?.isBothJoined) {
      throw new ClinicError(ERROR_TYPE.EDIT_RESERVATION_DEADLINE_PASSED);
    }

    // Transactionでキャンセルを処理する
    const runInTransaction = async <T>(fn: (tx: Transaction) => Promise<T>) => {
      if (trx) {
        return await fn(trx);
      } else {
        return await execWithTx(fn);
      }
    };

    const canceledReservationDtls = await runInTransaction(async (tx) => {
      // オンライン診療は服薬指導ついてくるのでそちらの予約もキャンセルする
      if (
        oldReserve.prescriptionReceiveMethod ===
          PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY &&
        oldReserve.pharmacyReserve
      ) {
        const { pharmacyReserve } = oldReserve;
        const { pharmacyReservationDetails } = pharmacyReserve;

        const cancelPharmacyReserveDtls = pharmacyReservationDetails.filter(
          (pharmacyReserveDtl) =>
            cancelReserveList.includes(pharmacyReserveDtl.reserveDetailId),
        );
        if (cancelPharmacyReserveDtls.length) {
          const cancelPharmacyReserveIds = cancelPharmacyReserveDtls.map(
            (dtl) => dtl.pharmacyReserveDetailId,
          );
          await Promise.all([
            // 服薬指導のレコードをキャンセルに更新する
            this.cancelPharmacyReserveDetailByIds(tx, cancelPharmacyReserveIds),
            // 履歴テーブルにレコードを追加
            this.insertPharmacyStatusHistoriesAfterCanceled(
              tx,
              cancelPharmacyReserveIds,
            ),
          ]);

          // 全員キャンセルの場合は服薬指導の予定日を更新する
          const shouldUpdateDesiredDate =
            pharmacyReservationDetails.length ===
              cancelPharmacyReserveIds.length &&
            pharmacyReserve.desiredDateStatus !==
              DESIRED_DATE_STATUS.CONFIGURED;
          if (shouldUpdateDesiredDate) {
            await this.updateDesiredDateWhenCancel(
              oldReserve.pharmacyReserve.pharmacyReserveId,
              tx,
            );
          }
        }
      }

      // 診療予約のキャンセル
      const cancelReservationDtls = oldReserve.reservationDetails.filter(
        (dtl) => cancelReserveList.includes(dtl.reserveDetailId),
      );
      if (cancelReservationDtls.length) {
        const isCancelAll =
          cancelReservationDtls.length === oldReserve.reservationDetails.length;
        await Promise.all([
          // reserve_detailをキャンセルに更新する
          this.cancelReserveDtls(
            tx,
            cancelReservationDtls,
            isCancelAll,
            cancelReasonType,
          ),
          // 履歴テーブルにレコードを追加
          this.insertReserveDtlHistoriesAfterCanceled(
            tx,
            cancelReservationDtls,
          ),
        ]);
      }

      // openSearchの予約可能数を更新する
      const examTimeSlotIds = new Set(
        oldReserve.reservationDetails
          .filter((reserveDetail) => reserveDetail.examTimeSlotId)
          .map((reserveDetail) => reserveDetail.examTimeSlotId),
      );

      for await (const examTimeSlotId of examTimeSlotIds) {
        const examTimeSlot = await this.examSlotService.getExamSlotById(
          examTimeSlotId,
        );
        await this.openSearchService.updateReservableSlots({
          examTimeSlotId: examTimeSlotId,
          reservableSlots: examTimeSlot.reservableSlots,
          hospitalId: examTimeSlot.calendar.hospitalId,
        });
      }

      return cancelReservationDtls;
    });

    if (!isNotSendMail) {
      // 予約キャンセルのお知らせのメールを送信
      await this.sendMailNotification(
        loginCustomer.email,
        cancelReserveList,
        TEMPLATE_MAIL_CODE.CANCEL_RESERVATION,
      );

      // クリニックに予約キャンセルのお知らせメールを送信
      await this.sendMailNotificationToClinic(
        cancelReserveList,
        TEMPLATE_MAIL_CODE.CANCEL_RESERVATION_CLINIC,
      );

      if (
        oldReserve.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
      ) {
        // 指定住所へ配送が設定されている予約の場合は、薬局24へメールを送付する。
        await this.sendMailNotificationToPharmacy(
          cancelReserveList,
          TEMPLATE_MAIL_CODE.TO_PHARMACY_FOR_CANCEL_RESERVE,
        );
      }
    }

    // hp_inf.karteStatusがリアル利用中の場合は、raiin_infを削除する。
    const hospitalInfo = oldReserve.patient?.hospital;

    if (
      canceledReservationDtls.length &&
      hospitalInfo?.karteStatus === HOSPITAL_KARTE_STATUS.REAL_IN_USE
    ) {
      // Delete raiin_inf
      const deleteRaiinInfoResList = await Promise.all(
        canceledReservationDtls.map(async (detail) => {
          const deleteRaiinInput =
            this.convertReserveDetailToDeleteRaiinInfInput(detail);
          if (!deleteRaiinInput) {
            return;
          }

          const res = await this.raiinService.deleteRaiinInfo(deleteRaiinInput);
          if (res.errors && res.errors.length > 0) {
            return {
              reserveDetailId: detail.reserveDetailId,
              errors: res.errors,
            };
          }
        }),
      );

      const deleteRaiinInfoErrorList = deleteRaiinInfoResList.filter(
        (res) => res !== undefined,
      );
      // 予約情報のエラーを処理
      handleReservationError(
        deleteRaiinInfoErrorList,
        '予約情報の削除に失敗しました',
      );
    }

    return { statusCode: HttpStatus.OK, reservations: oldReserve };
  }

  /**
   * 予約情報のメールを送信する関数
   * @param emailAddress
   * @param reservationIds
   * @param templateCode
   * @param isNewReservation
   * @returns
   */
  async sendMailNotification(
    emailAddress: string,
    reservationIds: number[],
    templateCode: string,
    isNewReservation?: boolean,
  ): Promise<void> {
    try {
      if (!reservationIds) {
        return;
      }

      const reserveInfos = await this.getReservationInfos(reservationIds);
      if (!reserveInfos.length) return;

      const values = this.buildMailValues(reserveInfos, isNewReservation);
      await this.mailService.sendMailByMailCode(
        emailAddress,
        templateCode,
        values as { [key: string]: string },
      );
    } catch (error) {
      this.logger.error('Error sending email notice to patient: ', error);
      Sentry.captureException(error);
    }
  }

  private buildMailValues(
    reserveInfos: ReservationInfo[],
    isNewReservation?: boolean,
  ) {
    if (!reserveInfos.length) {
      return {};
    }
    const reserveInfo = reserveInfos[0];
    const pharmacyInfo = reserveInfo.pharmacyInfo;
    const homePage = process.env.APP_URL;

    const reserveListUrl = isNewReservation
      ? `${homePage}/medical-appointment/edit/${reserveInfo.reserveId}`
      : `${homePage}/medical-appointment`;

    const patientsName = reserveInfos
      .map((r, index) => {
        return reserveInfos.length > 1
          ? `受診者${index + 1}：${r.patientName}`
          : `受診者：${r.patientName}`;
      })
      .join('\n');

    const pharmacyValues = pharmacyInfo
      ? {
          [TEMPLATE_MAIL_KEY.PRESCRIPTION_RECEIVE_METHOD]:
            pharmacyInfo.prescriptionReceiveMethod,
          [TEMPLATE_MAIL_KEY.PHARMACY_NAME]: pharmacyInfo.pharmacyName,
          [TEMPLATE_MAIL_KEY.PHARMACY_PHONE_NUMBER]:
            pharmacyInfo.pharmacyPhoneNumber,
          [TEMPLATE_MAIL_KEY.PHARMACY_FAX_NUMBER]:
            pharmacyInfo.pharmacyFaxNumber,
          [TEMPLATE_MAIL_KEY.PHARMACY_ADDRESS]: pharmacyInfo.pharmacyAddress,
          [TEMPLATE_MAIL_KEY.PHARMACY_PAYMENT]: pharmacyInfo.paymentMethod,
        }
      : {};

    return {
      [TEMPLATE_MAIL_KEY.SURVEY_LINK]: `${homePage}/survey?reserveDetail=${reserveInfo.reserveDetailId}`,
      [TEMPLATE_MAIL_KEY.RESERVATION_TIME]: convertExamTimePeriod(
        reserveInfo.examStartDate,
        reserveInfo.examEndDate,
      ),
      [TEMPLATE_MAIL_KEY.CLINIC_NAME]: reserveInfo.hospitalName,
      [TEMPLATE_MAIL_KEY.CLINIC_ADDRESS]: reserveInfo.hospitalAddress,
      [TEMPLATE_MAIL_KEY.CLINIC_PHONE_NUMBER]: reserveInfo.hospitalPhoneNumber,
      [TEMPLATE_MAIL_KEY.CLINIC_PAYMENT]: reserveInfo.paymentMethod,
      [TEMPLATE_MAIL_KEY.TREATMENT_DEPARTMENT]:
        reserveInfo.treatmentDepartmentName,
      [TEMPLATE_MAIL_KEY.RESERVATION_TYPE]: reserveInfo.reserveType,
      [TEMPLATE_MAIL_KEY.PATIENTS_NAME]: patientsName,
      [TEMPLATE_MAIL_KEY.RESERVATION_LIST_URL]: reserveListUrl,
      [TEMPLATE_MAIL_KEY.HAS_PHARMACY]: !!pharmacyInfo,
      ...pharmacyValues,
    };
  }

  /**
   * 予約情報のメールを送信する関数
   * @param emailAddress
   * @param reservationIds
   */
  async sendMailNotificationToClinic(
    reservationIds: number[],
    templateCode: string,
  ): Promise<void> {
    try {
      if (!reservationIds.length) {
        return;
      }

      // 家族予約に対して1stの情報を取得
      const reserveInfos = await this.getReservationInfos([reservationIds[0]]);
      const reserveInfo = reserveInfos[0];

      const mailAddresses = reserveInfo.hospital
        .staffs!.filter(
          ({ email, mailSetting }) => email && mailSetting?.allowReservation,
        )
        .map(({ email }) => email!);

      if (!mailAddresses.length) {
        return;
      }

      const values = {
        [TEMPLATE_MAIL_KEY.EXAM_TIME_PERIOD]: convertExamTimePeriod(
          reserveInfo.examStartDate,
          reserveInfo.examEndDate,
        ),
        [TEMPLATE_MAIL_KEY.TREATMENT_AND_RESERVE]: `${reserveInfo.treatmentType}/${reserveInfo.reserveType}`,
        [TEMPLATE_MAIL_KEY.CALENDAR_LABEL]: getCalendarName(
          reserveInfo.calendar,
        ),
        [TEMPLATE_MAIL_KEY.TREATMENT_DEPARTMENT]:
          reserveInfo.treatmentDepartmentName,
      };

      await this.mailService.sendMailByMailCode(
        mailAddresses,
        templateCode,
        values as { [key: string]: string },
      );
    } catch (error) {
      this.logger.error('Error sending email notice to clinic: ', error);
      Sentry.captureException(error);
    }
  }

  /**
   * 予約情報を取得する関数
   * @param reserveDtlIds
   * @returns
   */
  async getReservationInfos(
    reserveDtlIds: number[],
  ): Promise<ReservationInfo[]> {
    const reserveDtls = await this.reservationDetail
      .query()
      .where('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .whereIn('reserveDetail.reserveDetailId', reserveDtlIds)
      .withGraphFetched({
        reservation: {
          portalCustomerPharmacy: true,
        },
        paymentClinicDetail: true,
        pharmacyReserveDetail: {
          paymentPharmacyDetail: true,
        },
        patient: {
          hospital: {
            portalHospital: true,
          },
          portalCustomer: true,
        },
        calendarTreatment: {
          treatmentDepartment: true,
        },
        examTimeSlot: {
          calendar: {
            hospital: {
              staffs: {
                mailSetting: true,
              },
            },
            staff: true,
          },
        },
      });

    const reserveInfos = await Promise.all(
      reserveDtls.map(async (detail) => {
        const pharmacy = await this.getReservationPharmacyInfo(detail);
        const hospital = detail.patient.hospital.portalHospital;
        const clinicPayment = detail.paymentClinicDetail;

        const paymentMethod = getPaymentMethod({
          paymentType: clinicPayment?.paymentType,
          brand: clinicPayment?.brand,
          cardNo: clinicPayment?.cardNo,
          expire: clinicPayment?.expire,
        } as PaymentDetail);

        return {
          reserveDetailId: detail.reserveDetailId,
          reserveId: detail.reserveId,
          examStartDate: detail.examTimeSlot.examStartDate,
          examEndDate: detail.examTimeSlot.examEndDate,
          hospitalName: hospital.name,
          hospital: detail.examTimeSlot.calendar.hospital!,
          calendar: detail.examTimeSlot.calendar,
          hospitalAddress: `〒${formatZipCode(hospital.postCode)} ${
            hospital.address1 ?? ''
          }${hospital.address2 ?? ''}`,
          hospitalPhoneNumber: formatPhoneNumber(hospital.telephone),
          treatmentDepartmentName:
            detail.calendarTreatment.treatmentDepartment.title ?? '',
          reserveType:
            detail.reserveType === RESERVATION_TYPE.IN_PERSON
              ? '対面'
              : detail.reserveType === RESERVATION_TYPE.ONLINE
              ? 'オンライン'
              : '-',
          patientName: detail.patient.portalCustomer.name,
          paymentMethod: paymentMethod,
          treatmentType:
            detail.treatmentType === TREATMENT_TYPE.FIRST_EXAM
              ? '初診'
              : '再診',
          //薬局情報
          pharmacyInfo: pharmacy,
        } as ReservationInfo;
      }),
    );

    return reserveInfos;
  }

  async validateEditReserveUser(
    customer: Customer,
    reserveId: number,
  ): Promise<boolean> {
    const listFamilyMember =
      await this.customerService.getCustomerAndFamilyMemberByCustomerId(
        customer.customerId,
      );

    const listFamilyMemberIds = listFamilyMember.map(
      (member) => member.customerId,
    );

    const queryBuilder = this.reservationDetail
      .query()
      .joinRelated('patient')
      .withGraphFetched({
        patient: true,
        surveyAnswer: true,
        examDetails: true,
        reservation: {
          patient: true,
          meeting: true,
        },
        examTimeSlot: true,
        calendarTreatment: {
          treatmentDepartment: {
            treatmentCategory: true,
            hospital: {
              portalHospital: {
                stations: {
                  stationDetail: {
                    railline: true,
                  },
                },
              },
            },
          },
        },
      })
      .where('reserveDetail.isDeleted', IS_DELETED.FALSE);
    queryBuilder.andWhere(function () {
      this.whereIn('patient.portalCustomerId', listFamilyMemberIds);
    });

    const reservationDtls = await queryBuilder;

    const listReservationsMap: {
      [key: number]: Partial<Reservation> & {
        reservationDetails: ReservationDetail[];
      };
    } = reservationDtls.reduce(
      (
        acc: {
          [key: number]: Partial<Reservation> & {
            reservationDetails: ReservationDetail[];
          };
        },
        cur,
      ) => {
        if (!acc[cur.reserveId]) {
          acc[cur.reserveId] = {
            ...cur.reservation,
            reservationDetails: [],
          };
        }

        acc[cur.reserveId].reservationDetails.push(cur);

        return acc;
      },
      {},
    );

    return listReservationsMap[reserveId]?.reservationDetails ? true : false;
  }

  async hasReservePayment(cancelReserveList: [number]): Promise<boolean> {
    return (
      (await this.reservationDetail
        .query()
        .whereIn('reserveDetailId', cancelReserveList)
        .andWhere('paymentStatus', '!=', PAYMENT_STATUS.BEFORE)
        .resultSize()) > 0
    );
  }

  async getReserveById(
    customer: Customer,
    reserveId: number,
  ): Promise<ReservationResponse> {
    const listFamilyMember =
      await this.customerService.getCustomerAndFamilyMemberByCustomerId(
        customer.customerId,
      );

    const listFamilyMemberIds = listFamilyMember.map(
      ({ customerId }) => customerId,
    );

    const queryBuilder = this.reservationDetail
      .query()
      .joinRelated('[patient, reservation]')
      .withGraphJoined(
        '[reservation.portalCustomerPharmacy, reservation.pharmacyReserve]',
      )
      .withGraphFetched({
        patient: {
          portalCustomer: {
            customerSurvey: true,
          },
        },
        surveyAnswer: true,
        examDetails: true,
        examTimeSlot: {
          calendar: true,
        },
        reservation: {
          pharmacyReserve: {
            pharmacyDeliveryAddress: true,
            pharmacyReservationDetails: true,
          },
          portalCustomerPharmacy: true,
          meeting: true,
        },
        calendarTreatment: {
          calendar: true,
          treatmentDepartment: {
            firstMedicalInterviewForm: true,
            nextMedicalInterviewForm: true,
            treatmentCategory: true,
            hospital: {
              portalHospital: {
                stations: {
                  stationDetail: {
                    railline: true,
                  },
                },
              },
            },
          },
        },
        raiinInf: true,
      })
      .whereIn('patient.portalCustomerId', listFamilyMemberIds)
      .whereNot('status', RESERVATION_STATUS.CANCELED)
      .andWhere('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .andWhere('reservation.isDeleted', IS_DELETED.FALSE)
      .andWhere('patient.isDelete', IS_DELETED.FALSE)
      .modifyGraph('reservation.pharmacyReserve', (builder) => {
        builder.andWhere('isDeleted', IS_DELETED.FALSE);
      })
      .modifyGraph('reservation.portalCustomerPharmacy', (builder) => {
        builder.andWhere('isDeleted', IS_DELETED.FALSE);
      });
    const reservationDtls = await queryBuilder;

    const listReservationsMap: {
      [key: number]: Partial<Reservation> & {
        reservationDetails: ReservationDetail[];
      };
    } = reservationDtls.reduce(
      (
        acc: {
          [key: number]: Partial<Reservation> & {
            reservationDetails: ReservationDetail[];
          };
        },
        cur,
      ) => {
        if (!acc[cur.reserveId]) {
          acc[cur.reserveId] = {
            ...cur.reservation,
            reservationDetails: [],
          };
        }

        acc[cur.reserveId].reservationDetails.push(cur);

        return acc;
      },
      {},
    );

    if (!listReservationsMap[reserveId]) {
      throw new ClinicError(ERROR_TYPE.NOT_FOUND);
    }

    const reservation = {
      ...listReservationsMap[reserveId],
      reserveId: reserveId,
    };

    return {
      statusCode: HttpStatus.OK,
      reservation: reservation as Reservation,
    };
  }

  async getReservationDetailById(
    reserveDetailId: number,
    customer: Customer,
  ): Promise<ReservationDetailResponse | ReservationDetailError> {
    // get reservation detail
    const reserveDetail = await this.findReservationDetailByID(reserveDetailId);
    if (!reserveDetail) {
      throw new ClinicError(ERROR_TYPE.NOT_FOUND);
    }

    // check permission
    const isValid = await this.customerService.validateIsFamilyMember(
      customer,
      reserveDetail.patient.portalCustomerId || undefined,
    );

    if (!isValid) {
      throw new ClinicError(ERROR_TYPE.PERMISSION_DENIED);
    }

    // check if customer exists
    if (
      !reserveDetail.patient.portalCustomerId ||
      !(await this.customerService.findCustomerById(
        reserveDetail.patient.portalCustomerId,
      ))
    ) {
      throw new ClinicError(ERROR_TYPE.CUSTOMER_NOT_FOUND);
    }

    // get survey by treatment type
    const survey = reserveDetail.calendarTreatment
      ? await this.surveyService.getSurveyById(
          reserveDetail.treatmentType === TREATMENT_TYPE.FIRST_EXAM
            ? reserveDetail.calendarTreatment.treatmentDepartment
                .firstMedicalInterviewFormId
            : reserveDetail.calendarTreatment.treatmentDepartment
                .nextMedicalInterviewFormId,
        )
      : undefined;

    return {
      reservationDetail: reserveDetail,
      isCanceled: reserveDetail.isCanceled,
      hospital: reserveDetail.patient.hospital,
      customer: reserveDetail.patient.portalCustomer,
      patient: reserveDetail.patient,
      treatmentDepartmentTitle:
        reserveDetail.calendarTreatment.treatmentDepartment.title,
      survey,
    };
  }

  async isExistedReservation(
    customerIds: number[],
    examTimeSlot: ExamTimeSlot,
  ): Promise<boolean> {
    const result = await this.reservationDetail
      .query()
      .joinRelated('examTimeSlot.calendar')
      .joinRelated('patient')
      .whereIn('patient.portalCustomerId', customerIds)
      .whereNot('reserveDetail.status', RESERVATION_STATUS.CANCELED)
      .where('examTimeSlot.examStartDate', '>=', examTimeSlot.examStartDate)
      .where('examTimeSlot.examEndDate', '<=', examTimeSlot.examEndDate)
      .andWhere('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .andWhere('examTimeSlot.isDeleted', IS_DELETED.FALSE)
      .andWhere('examTimeSlot:calendar.isDeleted', IS_DELETED.FALSE)
      .andWhere('patient.isDelete', IS_DELETED.FALSE)
      .resultSize();

    return !!result;
  }

  async updateTreatmentStatusToCompleted(
    customer: Customer,
    reservationDetailIds: Array<number>,
  ): Promise<void> {
    const detailTrx = await Reservation.startTransaction();
    const meetingTrx = await Meeting.startTransaction();

    try {
      const familyMembers =
        await this.customerService.getCustomerAndFamilyMemberByCustomerId(
          customer.customerId,
        );

      const familyMemberIds = new Set(
        familyMembers.map(({ customerId }) => customerId),
      );

      const reservationDetails = await this.reservationDetail
        .query(detailTrx)
        .withGraphFetched('patient')
        .modifyGraph('patient', (builder) => {
          builder.where('isDelete', IS_DELETED.FALSE);
        })
        .whereIn('reserveDetailId', reservationDetailIds)
        .andWhere('reserveDetail.isDeleted', IS_DELETED.FALSE);

      if (
        reservationDetails.some(
          ({ patient: { portalCustomerId } }) =>
            !portalCustomerId || !familyMemberIds.has(portalCustomerId),
        )
      ) {
        throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
          extensions: {
            code: ERROR_TYPE.PERMISSION_DENIED,
          },
        });
      }

      await this.reservationDetail
        .query(detailTrx)
        .update({ status: RESERVATION_STATUS.APPOINTMENT_COMPLETED })
        .whereIn('reserveDetailId', reservationDetailIds)
        .andWhere('reserveDetail.isDeleted', IS_DELETED.FALSE);

      const reserveIds = reservationDetails.map((e) => e.reserveId);
      await this.meeting
        .query(meetingTrx)
        .update({ status: MEETING_STATUS.END })
        .whereIn('reserveId', reserveIds)
        .andWhere('isDeleted', IS_DELETED.FALSE);

      await detailTrx.commit();
      await meetingTrx.commit();
    } catch (error) {
      await detailTrx.rollback();
      await meetingTrx.rollback();
      throw error;
    }
  }

  async getCustomerTreatmentHistories(
    customer: Customer,
    hospital: PortalHospital,
    param: CustomersTreatmentHistoryInput,
  ): Promise<Array<CustomerTreatmentType>> {
    const treatmentHistories: Array<CustomerTreatmentType> = [];

    const familyMembers =
      await this.customerService.getCustomerAndFamilyMemberByCustomerId(
        customer.customerId,
      );
    const familyMemberIds = familyMembers.map(({ customerId }) => customerId);

    const patients = await this.patientService.getPatientByPortalCustomerIds(
      hospital.hpInfId,
      familyMemberIds,
    );
    const patientIds = new Set(
      patients.map(({ portalCustomerId }) => portalCustomerId),
    );

    const familyMembersWithoutExam = familyMemberIds.filter(
      (customerId) => !patientIds.has(customerId),
    );

    treatmentHistories.push(
      ...familyMembersWithoutExam.map((customerId) => ({
        customerId,
        treatmentType: TREATMENT_TYPE.FIRST_EXAM,
      })),
    );

    const familyMembersWithExam = patients.filter(({ portalCustomerId }) =>
      patientIds.has(portalCustomerId),
    );

    const patientsWithSpecificTreatment = await this.reservationDetail
      .query()
      .select('patientId')
      .joinRelated('calendarTreatment.treatmentDepartment')
      .whereIn(
        'patientId',
        familyMembersWithExam.map(({ ptId }) => ptId),
      )
      .whereNot('status', RESERVATION_STATUS.CANCELED)
      .where(
        'calendarTreatment:treatmentDepartment.treatmentDepartmentId',
        param.treatmentId,
      )
      .andWhere('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .andWhere(
        'calendarTreatment:treatmentDepartment.isDeleted',
        IS_DELETED.FALSE,
      )
      .groupBy('patientId')
      .withGraphFetched('patient')
      .modifyGraph('patient', (builder) => {
        builder.where('isDelete', IS_DELETED.FALSE);
      });

    treatmentHistories.push(
      ...patientsWithSpecificTreatment.map(
        ({ patient: { portalCustomerId } }) => ({
          customerId: Number(portalCustomerId),
          treatmentType: TREATMENT_TYPE.RE_EXAM,
        }),
      ),
    );

    const treatedPatientIds = new Set(
      patientsWithSpecificTreatment.map(({ patientId }) => patientId),
    );
    const familyMembersWithoutSpecificTreatment = familyMembersWithExam.filter(
      ({ ptId }) => !treatedPatientIds.has(ptId),
    );

    treatmentHistories.push(
      ...familyMembersWithoutSpecificTreatment.map(({ portalCustomerId }) => ({
        customerId: Number(portalCustomerId),
        treatmentType: TREATMENT_TYPE.FIRST_EXAM,
      })),
    );

    return treatmentHistories;
  }

  async isValidReservation(reserveDetailId: number): Promise<boolean> {
    const reserveDetail = await this.reservationDetail
      .query()
      .where('reserveDetailId', reserveDetailId)
      .andWhere('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .first()
      .withGraphFetched('examTimeSlot')
      .modifyGraph('examTimeSlot', (builder) => {
        builder.where('isDeleted', IS_DELETED.FALSE);
      });

    if (!reserveDetail || reserveDetail.isCanceled) return false;

    return true;
  }

  private async getPatientByPortalCustomer(
    customer: LoginCustomer,
    hospitalId: number,
  ): Promise<Patient> {
    const patient = await this.patientService.getPatientByPortalCustomerId(
      hospitalId,
      customer.customerId,
    );

    if (!patient) {
      return await this.patientService.registerPatient({
        hpId: hospitalId,
        ptNum: 0, // TODO
        portalCustomerId: customer.customerId,
        kanaName: `${customer.kanaName}`,
        name: `${customer.name}`,
        sex:
          customer.gender === GENDER_TYPE.MALE
            ? GENDER_TYPE.MALE
            : GENDER_TYPE.FEMALE,
        birthday: Number(dayjs(customer.birthday).format('YYYYMMDD')),
        primaryDoctor: 0, // TODO
        tel1: customer.telephone,
        mail: customer.email,
      });
    }

    return patient;
  }

  private calculateQueueNumber(examTimeSlot: ExamTimeSlot): number {
    const DEFAULT_START_QUEUE = 1;
    const calendarSettings = this.filterCalendarSettings(examTimeSlot);

    if (calendarSettings.length && calendarSettings[0].startWaitingNumber) {
      return calendarSettings[0].startWaitingNumber;
    }

    return DEFAULT_START_QUEUE;
  }

  private filterCalendarSettings(
    examTimeSlot: ExamTimeSlot,
  ): Array<{ startWaitingNumber?: number }> {
    const examTimeSlotStartTime = dayjs(examTimeSlot.examStartDate)
      .tz('Asia/Tokyo')
      .format('HH:mm');
    const examTimeSlotEndTime = dayjs(examTimeSlot.examEndDate)
      .tz('Asia/Tokyo')
      .format('HH:mm');

    const calendarSettings: Array<{ startWaitingNumber?: number }> = [];

    examTimeSlot.calendar.calendarBasicSettings.map(
      ({ startWaitingNumber, startDate, endDate, startTime, endTime }) => {
        const isValidSetting =
          dayjs(examTimeSlot.examStartDate).isBetween(startDate, endDate) &&
          examTimeSlotStartTime === startTime &&
          examTimeSlotEndTime === endTime;

        if (isValidSetting) {
          calendarSettings.push({
            startWaitingNumber,
          });
        }
      },
    );

    return calendarSettings;
  }

  private async getReservationDetail(
    { treatmentType, customerId }: CustomerTreatmentInput,
    examTimeSlot: ExamTimeSlot,
    loginCustomer: LoginCustomer,
    hospitalInfo: HospitalInfo,
    params: ReserveInput,
  ): Promise<PartialModelGraph<ReservationDetail>> {
    const customerInfo = await this.customerService.findCustomerById(
      customerId,
    );
    if (!customerInfo) throw new Error(ERROR_TYPE.CUSTOMER_NOT_FOUND);

    const patient = await this.getPatientByPortalCustomer(
      {
        ...customerInfo,
        email: loginCustomer.email,
        telephone: customerInfo.telephone || loginCustomer.telephone,
      } as LoginCustomer,
      hospitalInfo.hpId,
    );

    const reservationDetail: PartialModelGraph<ReservationDetail> = {
      treatmentType,
      ...MODEL_DEFAULT_VALUE,
      patientId: patient.ptId,
      status: RESERVATION_STATUS.RESERVED,
      reserveType: params.reserveType,
      calendarTreatmentId: params.calendarTreatmentId,
      examTimeSlotId: params.examTimeSlotId,
      isSuspended: false,
      memo: '',
      // オンライン診療の場合のみ、決済情報を保存する。
      ...(params.reserveType === RESERVATION_TYPE.ONLINE && {
        paymentCardId: params.paymentCardId,
        fincodeCustomerId: params.fincodeCustomerId,
        fincodeTenantId: hospitalInfo.finCodeInfo?.hpTenantShopId,
      }),
    };

    if (params.reservationMethod === RESERVATION_METHOD.ORDER) {
      const queueIDs = examTimeSlot.reservationDetails.flatMap(
        ({ queueId }) => queueId || 0,
      );

      const queueNumber = queueIDs.length
        ? Math.max(...queueIDs) + 1
        : this.calculateQueueNumber(examTimeSlot);
      reservationDetail.queueId = queueNumber;
    }

    return reservationDetail;
  }

  private async getPharmacyReservationDetail(
    { customerId }: CustomerTreatmentInput,
    pharmacyId: number,
    loginCustomer: LoginCustomer,
  ): Promise<PartialModelGraph<PharmacyReserveDetail>> {
    const customerInfo = await this.customerService.findCustomerById(
      customerId,
    );
    if (!customerInfo) throw new Error(ERROR_TYPE.CUSTOMER_NOT_FOUND);
    const patient = await this.getPatientByPortalCustomer(
      {
        ...customerInfo,
        email: loginCustomer.email,
        telephone: customerInfo.telephone || loginCustomer.telephone,
      } as LoginCustomer,
      pharmacyId,
    );

    return { ...MODEL_DEFAULT_VALUE, patientId: patient.ptId };
  }

  private async getPharmacyReserveInfo(
    loginCustomer: LoginCustomer,
    pharmacyId: number,
    params: ReserveInput,
  ): Promise<{
    pharmacyReserve?: PartialModelGraph<PharmacyReserve>;
    portalCustomerPharmacy?: PartialModelGraph<PortalCustomerPharmacy>;
  }> {
    if (params.reserveType !== RESERVATION_TYPE.ONLINE) return {};

    if (
      params.pharmacyDeliveryAddress &&
      params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
    ) {
      const patient = await this.getPatientByPortalCustomer(
        loginCustomer,
        pharmacyId,
      );

      const pharmacyReservationDetails = await Promise.all(
        params.customers.map((customer) =>
          this.getPharmacyReservationDetail(
            customer,
            pharmacyId,
            loginCustomer,
          ),
        ),
      );

      const pharmacyReserve = {
        ...MODEL_DEFAULT_VALUE,
        patientId: patient.ptId,
        pharmacyDeliveryAddress: {
          ...MODEL_DEFAULT_VALUE,
          ...params.pharmacyDeliveryAddress,
        },
        pharmacyReservationDetails,
        meetings: [
          {
            ...MODEL_DEFAULT_VALUE,
            patientId: patient.ptId,
            hospitalId: pharmacyId,
            status: MEETING_STATUS.RESERVED,
          },
        ],
      };

      return {
        pharmacyReserve,
      };
    }

    if (
      params.portalCustomerPharmacy &&
      params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY
    ) {
      const portalCustomerPharmacy = {
        ...MODEL_DEFAULT_VALUE,
        customerId: loginCustomer.customerId,
        ...params.portalCustomerPharmacy,
      };

      return {
        portalCustomerPharmacy,
      };
    }

    return {};
  }

  private async findReservationDetailByID(
    id: number,
  ): Promise<ReservationDetail | undefined> {
    return await this.reservationDetail
      .query()
      .where('reserveDetail.isDeleted', IS_DELETED.FALSE)
      .findById(id)
      .withGraphFetched({
        reservation: true,
        pharmacyReserveDetail: true,
        surveyAnswer: true,
        calendarTreatment: {
          treatmentDepartment: true,
        },
        patient: {
          hospital: {
            portalHospital: true,
          },
          portalCustomer: {
            customerSurvey: true,
          },
        },
      });
  }

  private async cancelReserveDtls(
    tx: Transaction,
    reservationDtls: ReservationDetail[],
    isCancelAll: boolean,
    cancelReasonType?: number,
  ): Promise<void> {
    const updateData = {
      status: RESERVATION_STATUS.CANCELED,
      reserveCancelType: cancelReasonType,
      isDeleted: !isCancelAll ? IS_DELETED.TRUE : undefined, // 部分キャンセルの場合、論理削除する
    };

    await this.reservationDetail
      .query(tx)
      .patch(updateData)
      .whereIn(
        'reserveDetailId',
        reservationDtls.map(({ reserveDetailId }) => reserveDetailId),
      );
  }

  private async insertReserveDtlHistoriesAfterCanceled(
    tx: Transaction,
    reservationDtls: ReservationDetail[],
  ): Promise<void> {
    if (reservationDtls.length) {
      const insertData = reservationDtls.map((reservationDtl) => ({
        createdAt: reservationDtl.updatedAt,
        updatedAt: reservationDtl.updatedAt,
        createdBy: reservationDtl.createdBy,
        updatedBy: reservationDtl.updatedBy,
        examDetailId: reservationDtl.examDetailId,
        status: RESERVATION_STATUS.CANCELED,
        reserveType: reservationDtl.reserveType,
        treatmentType: reservationDtl.treatmentType,
        memo: reservationDtl.memo,
        reserveDetailId: reservationDtl.reserveDetailId,
        examTimeSlotId: reservationDtl.examTimeSlotId,
        calendarTreatmentId: reservationDtl.calendarTreatmentId || 0,
      }));

      await this.reservationDetailHistory.query(tx).insert(insertData);
    }
  }

  private async cancelPharmacyReserveDetailByIds(
    tx: Transaction,
    pharmacyReserveIds: number[],
  ): Promise<void> {
    if (pharmacyReserveIds.length) {
      await this.pharmacyReserveDetail
        .query(tx)
        .whereIn('pharmacyReserveDetailId', pharmacyReserveIds)
        .patch({
          status: PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED,
        });
    }
  }

  private async insertPharmacyStatusHistoriesAfterCanceled(
    tx: Transaction,
    pharmacyReservationDtlIds: number[],
  ): Promise<void> {
    if (pharmacyReservationDtlIds.length) {
      const insertData = pharmacyReservationDtlIds.map(
        (pharmacyReserveDtlId) => ({
          pharmacyReserveDetailId: pharmacyReserveDtlId,
          status: PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED,
          statusCancelType:
            PHARMACY_STATUS_CANCEL_TYPE.PATIENT_CANCEL_VIA_RESERVATION,
        }),
      );

      await this.pharmacyReserveStatusHistory.query(tx).insert(insertData);
    }
  }

  private async updateReservation(
    reservation: Reservation,
    transaction: Transaction,
    updateRequest: PartialModelObject<Reservation>,
  ): Promise<void> {
    await reservation
      .$query(transaction)
      .where('isDeleted', IS_DELETED.FALSE)
      .patch(updateRequest);
  }

  private async createReservationDetailHistories(
    reservationDetails: ReservationDetail[],
    reservationDetailHistoryTrx: Transaction,
  ): Promise<void> {
    await Promise.all(
      reservationDetails.map(async (detail) => {
        await this.reservationDetailHistory
          .query(reservationDetailHistoryTrx)
          .insert({
            createdAt: detail.updatedAt,
            updatedAt: detail.updatedAt,
            createdBy: detail.createdBy,
            updatedBy: detail.updatedBy,
            examDetailId: detail.examDetailId,
            status: detail.status,
            reserveType: detail.reserveType,
            treatmentType: detail.treatmentType,
            memo: detail.memo,
            reserveDetailId: detail.reserveDetailId,
            examTimeSlotId: detail.examTimeSlotId,
            calendarTreatmentId: detail.calendarTreatmentId!,
          });
      }),
    );
  }

  private async updateReservationDetails(
    reservationDetails: ReservationDetail[],
    transaction: Transaction,
    updateRequest: PartialModelObject<ReservationDetail>,
  ): Promise<ReservationDetail[]> {
    await this.createReservationDetailHistories(
      reservationDetails,
      transaction,
    );

    const result = await this.reservationDetail
      .query(transaction)
      .patch(updateRequest)
      .whereIn(
        'reserveDetailId',
        reservationDetails.map(({ reserveDetailId }) => reserveDetailId),
      )
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .returning('*')
      .withGraphFetched({
        examTimeSlot: true,
        patient: true,
        calendarTreatment: true,
      });

    return result;
  }

  private async updatePharmacyAddress(
    params: UpdateReserveInput,
    currentReservation: Reservation,
    patientId: number,
    pharmacyId: number,
    loginCustomer: LoginCustomer,
    trx: Transaction,
  ): Promise<void> {
    const currentReservationDetail = currentReservation.reservationDetails;
    const pharmacyReservationDetails =
      await this.createPharmacyReserveDetailFromReserveDetail(
        currentReservationDetail,
        loginCustomer,
        pharmacyId,
      );
    if (
      params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY &&
      params.pharmacyDeliveryAddress
    ) {
      if (
        currentReservation.pharmacyReserve &&
        currentReservation.prescriptionReceiveMethod ===
          PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY &&
        currentReservation.pharmacyReserve.pharmacyReservationDetails.some(
          (dtl) => dtl.status !== PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED,
        )
      ) {
        await this.pharmacyDeliveryAddressService.editPharmacyDeliveryAddress(
          currentReservation.pharmacyReserve.pharmacyReserveId,
          params.pharmacyDeliveryAddress,
          trx,
        );
      } else {
        await this.updateReservePrescriptionMethod(
          currentReservation.reserveId,
          PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY,
          trx,
        );
        if (currentReservation.portalCustomerPharmacy) {
          await this.deletePortalCustomerPharmacyByReserveId(
            currentReservation.reserveId,
            trx,
          );
        }

        const currentPharmacyReserve = await this.pharmacyReserve
          .query(trx)
          .insertGraphAndFetch({
            ...MODEL_DEFAULT_VALUE,
            reserveId: currentReservation.reserveId,
            patientId: patientId,
            pharmacyReservationDetails: pharmacyReservationDetails,
            pharmacyDeliveryAddress: {
              ...MODEL_DEFAULT_VALUE,
              address1: params.pharmacyDeliveryAddress.address1,
              address2: params.pharmacyDeliveryAddress.address2,
              deliveryAddressId:
                params.pharmacyDeliveryAddress.deliveryAddressId,
              postCode: params.pharmacyDeliveryAddress.postCode,
              phoneNumber: params.pharmacyDeliveryAddress.phoneNumber,
            },
            meetings: [
              {
                ...MODEL_DEFAULT_VALUE,
                patientId: patientId,
                hospitalId: pharmacyId,
                status: MEETING_STATUS.RESERVED,
              },
            ],
          });

        await this.pharmacyService.insertPharmacyStatusHistoriesWhenReservePharmacy(
          trx,
          currentPharmacyReserve.pharmacyReservationDetails,
        );
      }
    }
    if (
      params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY &&
      params.portalCustomerPharmacy
    ) {
      if (
        currentReservation.portalCustomerPharmacy &&
        currentReservation.prescriptionReceiveMethod ===
          PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY
      ) {
        await this.portalCustomerPharmacy
          .query(trx)
          .where('reserveId', currentReservation.reserveId)
          .andWhere('isDeleted', IS_DELETED.FALSE)
          .update({
            address1: params.portalCustomerPharmacy.address1,
            address2: params.portalCustomerPharmacy.address2,
            pharmacyName: params.portalCustomerPharmacy.pharmacyName,
            pharmacyStoreName: params.portalCustomerPharmacy.pharmacyStoreName,
            faxNumber: params.portalCustomerPharmacy.faxNumber,
            postCode: params.portalCustomerPharmacy.postCode,
            phoneNumber: params.portalCustomerPharmacy.phoneNumber,
            updatedAt: new Date(),
            updatedBy: 'Customer',
          });
      } else {
        await this.reservation
          .query(trx)
          .where('reserveId', currentReservation.reserveId)
          .update({
            prescriptionReceiveMethod:
              PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY,
          });

        if (
          currentReservation.pharmacyReserve &&
          currentReservation.prescriptionReceiveMethod ===
            PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
        ) {
          await this.pharmacyDeliveryAddressService.deletePharmacyDeliveryAddress(
            currentReservation.pharmacyReserve.pharmacyReserveId,
            trx,
          );

          if (
            currentReservation.pharmacyReserve.meetings?.some(
              (meeting) => meeting.status !== MEETING_STATUS.DISABLED,
            )
          ) {
            await this.meeting
              .query(trx)
              .where(
                'pharmacyReserveId',
                currentReservation.pharmacyReserve.pharmacyReserveId,
              )
              .update({
                status: MEETING_STATUS.DISABLED,
              });

            await this.cancelPharmacyReserveDetailByPharmacyReserveId(
              currentReservation.pharmacyReserve.pharmacyReserveId,
              trx,
            );

            //update pharmacy reserve desired date
            await this.updateDesiredDateWhenCancel(
              currentReservation.pharmacyReserve.pharmacyReserveId,
              trx,
            );
          }
        }
        await this.portalCustomerPharmacy.query(trx).insertGraphAndFetch({
          ...MODEL_DEFAULT_VALUE,
          customerId: loginCustomer.customerId,
          reserveId: currentReservation.reserveId,
          address1: params.portalCustomerPharmacy.address1,
          address2: params.portalCustomerPharmacy.address2,
          pharmacyName: params.portalCustomerPharmacy.pharmacyName,
          pharmacyStoreName: params.portalCustomerPharmacy.pharmacyStoreName,
          postCode: params.portalCustomerPharmacy.postCode,
          faxNumber: params.portalCustomerPharmacy.faxNumber,
          phoneNumber: params.portalCustomerPharmacy.phoneNumber,
        });
      }
    }

    if (
      params.prescriptionReceiveMethod ===
      PHARMACY_PRESCRIPTION_RECEIVE_METHOD.NOT_SPECIFY
    ) {
      await this.updateReservePrescriptionMethod(
        currentReservation.reserveId,
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.NOT_SPECIFY,
        trx,
      );
      if (
        currentReservation.pharmacyReserve &&
        currentReservation.prescriptionReceiveMethod ===
          PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY
      ) {
        await this.cancelPharmacyReserveDetailByPharmacyReserveId(
          currentReservation.pharmacyReserve.pharmacyReserveId,
          trx,
        );
        await this.pharmacyDeliveryAddressService.deletePharmacyDeliveryAddress(
          currentReservation.pharmacyReserve.pharmacyReserveId,
          trx,
        );

        if (
          currentReservation.pharmacyReserve.meetings.some(
            (meeting) => meeting.status !== MEETING_STATUS.DISABLED,
          )
        ) {
          await this.meeting
            .query(trx)
            .where(
              'pharmacyReserveId',
              currentReservation.pharmacyReserve.pharmacyReserveId,
            )
            .update({
              status: MEETING_STATUS.DISABLED,
            });
        }

        //update pharmacy reserve desired date
        await this.updateDesiredDateWhenCancel(
          currentReservation.pharmacyReserve.pharmacyReserveId,
          trx,
        );
      }
      if (currentReservation.portalCustomerPharmacy) {
        await this.deletePortalCustomerPharmacyByReserveId(
          currentReservation.reserveId,
          trx,
        );
      }
    }
  }

  private async cancelCustomers(
    loginCustomer: LoginCustomer,
    customers: CustomerTreatmentInput[],
    currentReservation: Reservation,
    trx: Transaction,
  ): Promise<number[]> {
    // 顧客配列をセットに変換する
    const customerIdsSet = new Set(
      customers.map((customer) => customer.customerId),
    );

    // 予約の詳細をフィルタリングしてIDを抽出する
    const canceledReserveDetailIds = currentReservation.reservationDetails
      .filter((reservationDetail) => {
        const customerId = reservationDetail.patient.portalCustomerId;
        return customerId && !customerIdsSet.has(customerId);
      })
      .map((reservationDetail) => reservationDetail.reserveDetailId);

    // 一致するcustomerがない場合はキャンセルされた扱いなので更新する
    if (canceledReserveDetailIds.length > 0) {
      await this.cancelReserve(
        loginCustomer,
        currentReservation.reserveId,
        canceledReserveDetailIds,
        undefined,
        true,
        trx,
      );
    }

    return canceledReserveDetailIds;
  }

  private async createPharmacyReserveDetailFromReserveDetail(
    reservationDetail: ReservationDetail[],
    loginCustomer: LoginCustomer,
    pharmacyId: number,
  ): Promise<PartialModelGraph<PharmacyReserveDetail>[]> {
    const pharmacyReserveDetailInputs: PartialModelGraph<PharmacyReserveDetail>[] =
      [];
    for (const resDetail of reservationDetail) {
      if (resDetail.patientId) {
        const customer = resDetail.patient.portalCustomer;
        if (customer) {
          const currentCustomer = new LoginCustomer();
          Object.assign(currentCustomer, customer);
          currentCustomer.email = loginCustomer.email;
          const patient = await this.getPatientByPortalCustomer(
            currentCustomer,
            pharmacyId,
          );
          const pharmacyReserveDetailInput: PartialModelGraph<PharmacyReserveDetail> =
            {
              patientId: patient.ptId,
              reserveDetailId: resDetail.reserveDetailId,
            };
          pharmacyReserveDetailInputs.push(pharmacyReserveDetailInput);
        }
      }
    }

    return pharmacyReserveDetailInputs;
  }

  private async updateReservePrescriptionMethod(
    reserveId: number,
    prescriptionMethod: number,
    trx: Transaction,
  ): Promise<void> {
    await this.reservation.query(trx).where('reserveId', reserveId).update({
      prescriptionReceiveMethod: prescriptionMethod,
    });
  }

  private async cancelPharmacyReserveDetailByPharmacyReserveId(
    pharmacyReserveId: number,
    trx: Transaction,
  ): Promise<void> {
    const currentPharmacyReserveDetail = await this.pharmacyReserveDetail
      .query(trx)
      .where('pharmacyReserveId', pharmacyReserveId)
      .whereNot('status', PHARMACY_RESERVE_DETAIL_STATUS.CANCELLED)
      .andWhere('isDeleted', IS_DELETED.FALSE);

    const currentPharmacyReserveDetailIds = currentPharmacyReserveDetail.map(
      (dtl) => dtl.pharmacyReserveDetailId,
    );

    await Promise.all([
      this.cancelPharmacyReserveDetailByIds(
        trx,
        currentPharmacyReserveDetailIds,
      ),
      this.insertPharmacyStatusHistoriesAfterCanceled(
        trx,
        currentPharmacyReserveDetailIds,
      ),
    ]);
  }

  private async deletePortalCustomerPharmacyByReserveId(
    reserveId: number,
    trx: Transaction,
  ): Promise<void> {
    await this.portalCustomerPharmacy
      .query(trx)
      .where('reserveId', reserveId)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .update({ isDeleted: IS_DELETED.TRUE });
  }

  /**
   *  @deprecated 複数薬局が登録されるようになると薬局24の情報が一意に取得できないので getYakkyoku24HospitalInfo を使用すること
   */
  async findHospitalPharmacy(): Promise<HospitalInfo> {
    const pharmacyData = await this.hospital
      .query()
      .where('pharmacyFlg', true)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .first()
      .throwIfNotFound();
    return pharmacyData;
  }

  async isExistSameDayReservation(
    customerIds: number[],
    examTimeSlotId: number,
  ): Promise<boolean> {
    const examTimeSlot = await this.examSlotService.getExamSlotById(
      examTimeSlotId,
    );

    return this.isExistedReservation(customerIds, examTimeSlot);
  }

  private async updateDesiredDateWhenCancel(
    pharmacyReserveId: number,
    tx: Transaction,
  ): Promise<void> {
    // update pharmacy reserve desired date status to configured
    await this.pharmacyReserve
      .query(tx)
      .patch({
        desiredDateStatus: DESIRED_DATE_STATUS.CONFIGURED,
      })
      .findById(pharmacyReserveId);

    // delete all current available desired date
    await this.pharmacyDesiredDate
      .query(tx)
      .where('pharmacyReserveId', pharmacyReserveId)
      .where('isDeleted', IS_DELETED.FALSE)
      .softDelete();

    // insert new desired date
    await this.pharmacyDesiredDate.query(tx).insert({
      pharmacyReserveId,
      desiredDate: new Date(),
      desiredType: DESIRED_TYPE.SPECIFY,
    });
  }

  /**
   * 薬局情報を取得する関数
   * @param reservation
   * @returns
   */
  async getReservationPharmacyInfo(
    reservationDtl: ReservationDetail,
  ): Promise<ReservationPharmacyInfo | null> {
    const { prescriptionReceiveMethod, portalCustomerPharmacy } =
      reservationDtl.reservation;
    if (
      reservationDtl.reserveType === RESERVATION_TYPE.IN_PERSON ||
      prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.NOT_SPECIFY
    ) {
      return null;
    }

    const paymentInfo =
      reservationDtl.pharmacyReserveDetail?.paymentPharmacyDetail;
    let pharmacy: ReservationPharmacyInfo = {
      prescriptionReceiveMethod: getPrescriptionReceiveMethod(
        prescriptionReceiveMethod,
      ),
      paymentMethod: getPaymentMethod({
        paymentType: paymentInfo?.paymentType,
        brand: paymentInfo?.brand,
        cardNo: paymentInfo?.cardNo,
        expire: paymentInfo?.expire,
      } as PaymentDetail),
    } as ReservationPharmacyInfo;

    switch (prescriptionReceiveMethod) {
      case PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY: {
        const yakkyoku24HospitalInfo = await this.getYakkyoku24HospitalInfo();
        pharmacy = {
          ...pharmacy,
          pharmacyName: yakkyoku24HospitalInfo.hpName,
          pharmacyPhoneNumber: formatPhoneNumber(
            yakkyoku24HospitalInfo.tel ?? '',
          ), // telは薬局24のIVR電話番号
          pharmacyFaxNumber: formatPhoneNumber(
            yakkyoku24HospitalInfo.faxNo ?? '',
          ),
          pharmacyAddress: `〒${formatZipCode(
            yakkyoku24HospitalInfo.postCd ?? '',
          )} ${yakkyoku24HospitalInfo.address1 ?? ''} ${
            yakkyoku24HospitalInfo.address2 ?? ''
          }`,
        };
        break;
      }
      case PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY:
        pharmacy = {
          ...pharmacy,
          pharmacyName: portalCustomerPharmacy?.pharmacyName,
          pharmacyPhoneNumber: formatPhoneNumber(
            portalCustomerPharmacy?.phoneNumber,
          ),
          pharmacyFaxNumber: formatPhoneNumber(
            portalCustomerPharmacy?.faxNumber,
          ),
          pharmacyAddress: `〒${formatZipCode(
            portalCustomerPharmacy?.postCode,
          )} ${portalCustomerPharmacy.address1 ?? ''} ${
            portalCustomerPharmacy.address2 ?? ''
          }`,
        };
        break;
    }
    return pharmacy;
  }

  private async getReserveIdsByCondition(
    customerIds: number[],
    type: RESERVER_REQUEST_TYPE,
  ): Promise<number[]> {
    const patients = await this.patient
      .query()
      .select('ptId')
      .whereIn('portalCustomerId', customerIds)
      .where('isDelete', IS_DELETED.FALSE);

    if (patients.length === 0) return [];

    let reserves: Reservation[] = [];

    if (type === RESERVER_REQUEST_TYPE.UP_COMING_RESERVE) {
      // query all reservation that has at least one reservation detail not completed
      reserves = await this.reservation
        .query()
        .innerJoinRelated('reservationDetails')
        .select('reserve.reserveId')
        .where('reserve.isDeleted', IS_DELETED.FALSE)
        .whereIn(
          'reservationDetails.patientId',
          patients.map(({ ptId }) => ptId),
        )
        .whereNotIn('status', [
          RESERVATION_STATUS.CANCELED,
          RESERVATION_STATUS.APPOINTMENT_COMPLETED,
        ])
        .where('reservationDetails.isDeleted', IS_DELETED.FALSE)
        .distinct('reserve.reserveId');
    } else {
      // query all reservation that has all reservation detail completed
      reserves = await this.reservation
        .query()
        .innerJoinRelated('reservationDetails')
        .select('reserve.reserveId')
        .where('reserve.isDeleted', IS_DELETED.FALSE)
        .whereIn(
          'reservationDetails.patientId',
          patients.map(({ ptId }) => ptId),
        )
        .where('reservationDetails.isDeleted', IS_DELETED.FALSE)
        .groupBy('reserve.reserveId')
        .havingRaw(
          `COUNT(*) = SUM(CASE WHEN status = ${RESERVATION_STATUS.CANCELED} OR status = ${RESERVATION_STATUS.APPOINTMENT_COMPLETED} THEN 1 ELSE 0 END) AND COUNT(*) > 0`,
        );
    }

    return reserves.map(({ reserveId }) => reserveId);
  }

  async isExistPaymentError(customerId: number): Promise<boolean> {
    const clinicPaymentErrorAmount = await this.paymentClinicDetail
      .query()
      .where('customerId', customerId)
      .where('paymentStatus', PAYMENT_STATUS.FAILED)
      .where('paymentType', PAYMENT_TYPE.CARD)
      .where('actionType', PAYMENT_ACTION_TYPE.NEW)
      .where('isDeleted', IS_DELETED.FALSE)
      .resultSize();

    const pharmacyPaymentErrorAmount = await this.paymentPharmacyDetail
      .query()
      .where('customerId', customerId)
      .where('paymentStatus', PAYMENT_STATUS.FAILED)
      .where('paymentType', PAYMENT_TYPE.CARD)
      .where('actionType', PAYMENT_ACTION_TYPE.NEW)
      .where('isDeleted', IS_DELETED.FALSE)
      .resultSize();

    return !!(clinicPaymentErrorAmount || pharmacyPaymentErrorAmount);
  }

  async getYakkyoku24HospitalInfo(): Promise<HospitalInfo> {
    if (!process.env.YAKKYOKU24_HPID) {
      throw new Error('YAKKYOKU24_HPID is not set');
    }

    const yakkyoku24HospitalInfo = await this.hospital
      .query()
      .where('hpId', process.env.YAKKYOKU24_HPID)
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .first()
      .throwIfNotFound();
    return yakkyoku24HospitalInfo;
  }

  async validateDeliveryAddress(
    loginCustomerId: number,
    deliveryAddressId: number,
  ): Promise<boolean> {
    const family = await this.customer
      .query()
      .where('parentID', loginCustomerId)
      .andWhere('isDeleted', IS_DELETED.FALSE);

    const isValid = await this.customerDeliveryAddress
      .query()
      .where('deliveryAddressId', deliveryAddressId)
      .whereIn('customerId', [
        loginCustomerId,
        ...family.map(({ customerId }) => customerId),
      ])
      .andWhere('isDeleted', IS_DELETED.FALSE)
      .first();

    return !!isValid;
  }

  private convertReserveDetailToRegisterRaiinInfInput(
    reserveDetail: ReservationDetail,
  ): BookingFromCalendarOrPortalInput {
    const {
      examTimeSlot,
      patient,
      calendarTreatment,
      reserveId,
      reserveDetailId,
      patientId,
      memo,
    } = reserveDetail;

    const dateNumber = Number(
      dayjs(examTimeSlot.examStartDate).format('YYYYMMDD'),
    );

    const input: BookingFromCalendarOrPortalInput = {
      raiinComment: memo,
      receptionModel: {
        hpId: patient.hpId,
        ptId: patientId || patient.ptId,
        sinDate: dateNumber,
        yoyakuId: reserveId,
        yoyakuTime: getDayjsDateJST(examTimeSlot.examStartDate).format(
          'HHmmss',
        ),
        yoyakuEndTime: getDayjsDateJST(examTimeSlot.examEndDate).format(
          'HHmmss',
        ),
        treatmentDepartmentId: calendarTreatment.treatmentDepartmentId,
        reserveDetailId: reserveDetailId,
        tantoId: examTimeSlot.calendar.doctorId ?? undefined,
      },
    };

    return input;
  }

  private convertReserveDetailToDeleteRaiinInfInput(
    reserveDetail: ReservationDetail,
  ): UpdateReceptionStaticCellRequestInput | null {
    const raiinInf = reserveDetail.raiinInf;
    if (!raiinInf) {
      return null;
    }

    const { hpId, raiinNo, sinDate, ptId } = raiinInf;
    const input: UpdateReceptionStaticCellRequestInput = {
      hpId,
      cellName: 'Status',
      cellValue: '9', // 削除ステータス（固定）
      raiinNo,
      sinDate,
      ptId,
    };

    return input;
  }

  private convertReserveDetailToUpdateRaiinInfoInput(
    reserveDetails: ReservationDetail[],
  ): UpdateBookingInfoInput | null {
    if (!reserveDetails.length) {
      return null;
    }

    // get reserve info by the first record
    const { examTimeSlot, patient, calendarTreatment, reserveId } =
      reserveDetails[0];

    if (!calendarTreatment) {
      return null;
    }

    const dateNumber = Number(
      dayjs(examTimeSlot.examStartDate).format('YYYYMMDD'),
    );
    const reserveDetailIds = reserveDetails.map(
      ({ reserveDetailId }) => reserveDetailId,
    );
    const input: UpdateBookingInfoInput = {
      hpId: patient.hpId,
      reserveDetailIds,
      sinDate: dateNumber,
      treatmentDepartmentId: calendarTreatment.treatmentDepartmentId,
      userId: reserveId,
      yoyakuTime: getDayjsDateJST(examTimeSlot.examStartDate).format('HHmmss'),
      yoyakuEndTime: getDayjsDateJST(examTimeSlot.examEndDate).format('HHmmss'),
    };

    return input;
  }

  /**
   * 予約情報のメールを送信する関数
   * @param emailAddress
   * @param reservationIds
   */
  async sendMailNotificationToPharmacy(
    reservationIds: number[],
    templateCode: string,
  ): Promise<void> {
    if (!process.env.EMAIL_ADDRESS_YAKKYOKU24_OFFICE) {
      throw new Error('EMAIL_ADDRESS_YAKKYOKU24_OFFICE is not set');
    }

    try {
      const mailAddresses = process.env.EMAIL_ADDRESS_YAKKYOKU24_OFFICE;

      if (!mailAddresses.length) {
        return;
      }

      const values = {};

      await this.mailService.sendMailByMailCode(
        mailAddresses,
        templateCode,
        values as { [key: string]: string },
      );
    } catch (error) {
      this.logger.error('Error sending email notice to pharmacy: ', error);
      Sentry.captureException(error);
    }
  }
}
