import { BadRequestException, HttpStatus, UseGuards } from '@nestjs/common';
import { Args, Int, Mutation, Query, Resolver } from '@nestjs/graphql';
import { GraphQLError } from 'graphql';

import { CalendarTreatmentService } from '@/calendar-treatment/calendar-treatment.service';
import { RESERVATION_ERR, RESERVER_REQUEST_TYPE } from '@/common/constants';
import { ERROR_TYPE } from '@/common/constants/error-type';
import {
  PHARMACY_PRESCRIPTION_RECEIVE_METHOD,
  RAINIIN_STATUS,
  RESERVABLE_SLOT_SETTING_TYPE,
  TREATMENT_TYPE,
} from '@/common/constants/master-type';
import { CurrentCustomer } from '@/common/decorators/login-customer.decorator';
import { ActiveAgreeGuard } from '@/common/guards/agree/active-agree.guard';
import {
  ActiveCustomerLoginGuard,
  RegistrationCompletionGuard,
} from '@/common/guards/customer/active-customer-login.guard';
import { ClinicError } from '@/common/utils/error';
import { canReserveSlot } from '@/common/utils/reservation';
import { FincodeService } from '@/fincode/fincode.service';
import { HospitalService } from '@/hospital/hospital.service';
import PharmacyService from '@/pharmacy/pharmacy.service';

import { isGreaterThanNow } from '../common/utils/date-time';
import { LoginCustomer } from '../customer/customer.output';
import { CustomerService } from '../customer/customer.service';
import { ExamTimeSlotService } from '../exam-time-slot/exam-time-slot.service';
import { PatientService } from '../patient/patient.service';
import {
  CustomersTreatmentHistoryInput,
  IsExistSameDayReservationInput,
  ReservationDetailsByConditionsInput,
  ReserveInput,
  UpdateReserveInput,
} from './reservation.input';
import {
  CustomerTreatmentType,
  ReservationDetailError,
  ReservationDetailResponse,
  ReservationOrPharmacyReserve,
  ReservationResponse,
  ReserveCancelResponse,
} from './reservation.output';
import ReservationService from './reservation.service';

@Resolver()
export default class ReservationResolver {
  constructor(
    private reservationService: ReservationService,
    private examTimeSlotService: ExamTimeSlotService,
    private customerService: CustomerService,
    private patientService: PatientService,
    private hospitalService: HospitalService,
    private calendarTreatmentService: CalendarTreatmentService,
    private pharmacyService: PharmacyService,
    private fincodeService: FincodeService,
  ) {}

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Mutation(() => ReservationResponse)
  async reserve(
    @Args('params', { type: () => ReserveInput }) params: ReserveInput,
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<ReservationResponse> {
    params.customerId = loginCustomer.customerId;

    if (
      (params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY &&
        !params.pharmacyDeliveryAddress) ||
      (params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY &&
        !params.portalCustomerPharmacy)
    ) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.PHARMACY_DELIVERY_ADDRESS_REQUIRED,
      };
    }

    const isExistPaymentError =
      await this.reservationService.isExistPaymentError(
        loginCustomer.customerId,
      );

    if (isExistPaymentError) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.EXIST_PAYMENT_ERROR,
      };
    }

    const examTimeSlot = await this.examTimeSlotService.getExamSlotById(
      Number(params.examTimeSlotId),
      params.calendarTreatmentId,
    );

    if (!isGreaterThanNow(examTimeSlot.examEndDate)) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.OUT_OF_TIME,
      };
    }

    if (!canReserveSlot(examTimeSlot)) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.RE_SETTING_TIME_SLOT,
      };
    }

    if (examTimeSlot.isSuspended) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.OUT_OF_SLOT,
      };
    }

    if (
      await this.reservationService.isExistedReservation(
        params.customers.map(({ customerId }) => customerId),
        examTimeSlot,
      )
    ) {
      return {
        statusCode: HttpStatus.CONFLICT,
        errorMessage: RESERVATION_ERR.DUPLICATED,
      };
    }

    // From wished examination slot -> Check if this slot is available to reserve
    const isByFixedNumberOfPatients =
      examTimeSlot.calendar.reservableSlotSettingType ===
      RESERVABLE_SLOT_SETTING_TYPE.BY_FIX_NUMBER_OF_PATIENT;

    let totalConsultantTimes = params.customers.length;
    if (!isByFixedNumberOfPatients) {
      const calendarTreatment =
        await this.calendarTreatmentService.getCalendarTreatmentById(
          params.calendarTreatmentId,
        );

      totalConsultantTimes = params.customers.reduce(
        (accumulator, { treatmentType }) =>
          accumulator +
          Number(
            treatmentType === TREATMENT_TYPE.FIRST_EXAM
              ? calendarTreatment.treatmentDepartment.firstConsultationTime
              : calendarTreatment.treatmentDepartment.nextConsultationTime,
          ),
        0,
      );
    }

    if (examTimeSlot.reservableSlots < totalConsultantTimes) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.OUT_OF_SLOT,
      };
    }

    // Throw error if customer is reserved
    const hospital = await this.hospitalService.getHospital(params.hospitalId);

    const patients = await this.patientService.getPatientByPortalCustomerIds(
      hospital.hpInfId,
      params.customers.map(({ customerId }) => customerId),
    );

    const reservedPatientIds = examTimeSlot.reservationDetails.map(
      ({ patientId }) => patientId,
    );

    if (patients.some(({ ptId }) => reservedPatientIds.includes(ptId))) {
      return {
        statusCode: HttpStatus.CONFLICT,
        errorMessage: RESERVATION_ERR.HISTORY_RESERVED,
      };
    }

    if (params.pharmacyDeliveryAddress?.deliveryAddressId) {
      const validDeliveryAddress =
        await this.reservationService.validateDeliveryAddress(
          loginCustomer.customerId,
          params.pharmacyDeliveryAddress.deliveryAddressId,
        );
      if (!validDeliveryAddress) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          errorMessage: ERROR_TYPE.PERMISSION_DENIED,
        };
      }
    }

    return {
      statusCode: HttpStatus.CREATED,
      reservation: await this.reservationService.reserve(params, loginCustomer),
    };
  }

  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  @Mutation(() => ReservationResponse)
  async updateReservation(
    @Args('params', { type: () => UpdateReserveInput })
    params: UpdateReserveInput,
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<ReservationResponse> {
    if (
      (params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY &&
        !params.pharmacyDeliveryAddress) ||
      (params.prescriptionReceiveMethod ===
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY &&
        !params.portalCustomerPharmacy)
    ) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.PHARMACY_DELIVERY_ADDRESS_REQUIRED,
      };
    }

    const currentReservation = await this.reservationService.getReserveById(
      loginCustomer,
      params.reserveId,
    );

    if (!currentReservation.reservation?.reservationDetails?.length) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
      };
    }

    const raiinInfs = currentReservation.reservation?.reservationDetails.map(
      (detail) => detail.raiinInf,
    );

    if (
      raiinInfs.some(
        (raiinInfo) => raiinInfo && raiinInfo.status >= RAINIIN_STATUS.VERIFIED,
      )
    ) {
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        errorMessage: RESERVATION_ERR.RECEPTION_CONNECTED,
      };
    }

    const {
      examTimeSlot: currentExamTimeSlot,
      reserveType,
      calendarTreatmentId,
      calendarTreatment: {
        treatmentDepartment: {
          hospital: { portalHospital },
        },
      },
    } = currentReservation.reservation.reservationDetails[0];

    const requestToUpdateExamTimeSlot =
      await this.examTimeSlotService.getExamSlotById(
        Number(params.examTimeSlotId),
        Number(calendarTreatmentId),
      );

    if (
      currentExamTimeSlot.examTimeSlotId !==
      requestToUpdateExamTimeSlot.examTimeSlotId
    ) {
      if (!isGreaterThanNow(requestToUpdateExamTimeSlot.examEndDate)) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          errorMessage: RESERVATION_ERR.OUT_OF_TIME,
        };
      }

      if (!canReserveSlot(requestToUpdateExamTimeSlot)) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          errorMessage: RESERVATION_ERR.RE_SETTING_TIME_SLOT,
        };
      }

      if (requestToUpdateExamTimeSlot.isSuspended) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          errorMessage: RESERVATION_ERR.OUT_OF_SLOT,
        };
      }

      const customers = currentReservation.reservation.reservationDetails.map(
        (detail) => ({
          customerId: Number(detail.patient.portalCustomerId),
          treatmentType: detail.treatmentType,
        }),
      );

      if (
        await this.reservationService.isExistedReservation(
          customers.map(({ customerId }) => customerId),
          requestToUpdateExamTimeSlot,
        )
      ) {
        return {
          statusCode: HttpStatus.CONFLICT,
          errorMessage: RESERVATION_ERR.DUPLICATED,
        };
      }

      // From wished examination slot -> Check if this slot is available to reserve
      const isByFixedNumberOfPatients =
        reserveType === RESERVABLE_SLOT_SETTING_TYPE.BY_FIX_NUMBER_OF_PATIENT;

      let totalConsultantTimes = customers.length;
      if (!isByFixedNumberOfPatients) {
        const calendarTreatment =
          await this.calendarTreatmentService.getCalendarTreatmentById(
            calendarTreatmentId!,
          );

        totalConsultantTimes = customers.reduce(
          (accumulator, { treatmentType }) =>
            accumulator +
            Number(
              treatmentType === TREATMENT_TYPE.FIRST_EXAM
                ? calendarTreatment.treatmentDepartment.firstConsultationTime
                : calendarTreatment.treatmentDepartment.nextConsultationTime,
            ),
          0,
        );
      }

      if (requestToUpdateExamTimeSlot.reservableSlots < totalConsultantTimes) {
        return {
          statusCode: HttpStatus.BAD_REQUEST,
          errorMessage: RESERVATION_ERR.OUT_OF_SLOT,
        };
      }

      const patients = await this.patientService.getPatientByPortalCustomerIds(
        portalHospital.hpInfId,
        customers.map(({ customerId }) => customerId),
      );

      const reservedPatientIds =
        requestToUpdateExamTimeSlot.reservationDetails.map(
          ({ patientId }) => patientId,
        );

      if (patients.some(({ ptId }) => reservedPatientIds.includes(ptId))) {
        return {
          statusCode: HttpStatus.CONFLICT,
          errorMessage: RESERVATION_ERR.HISTORY_RESERVED,
        };
      }
    }

    await this.reservationService.updateReserve(params, loginCustomer);

    return {
      statusCode: HttpStatus.OK,
    };
  }

  @Mutation(() => ReserveCancelResponse)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async cancelReserve(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('reserveId', { type: () => Int, nullable: false })
    reserveId: number,
    @Args('cancelReserveList', { type: () => [Int], nullable: true })
    cancelReserveList: [number],
    @Args('reasonType', { type: () => Int, nullable: true })
    reasonType?: number,
  ): Promise<ReserveCancelResponse> {
    if (
      !(await this.reservationService.validateEditReserveUser(
        loginCustomer,
        reserveId,
      ))
    ) {
      throw new ClinicError(ERROR_TYPE.PERMISSION_DENIED);
    }

    if (await this.reservationService.hasReservePayment(cancelReserveList)) {
      throw new ClinicError(ERROR_TYPE.RESERVATION_PAYMENT_EXISTED);
    }

    return await this.reservationService.cancelReserve(
      loginCustomer,
      reserveId,
      cancelReserveList,
      reasonType,
    );
  }

  @Query(() => ReservationResponse)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getReserveById(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('reserveId', { type: () => Int, nullable: false })
    reserveId: number,
  ): Promise<ReservationResponse> {
    if (
      !(await this.reservationService.validateEditReserveUser(
        loginCustomer,
        reserveId,
      ))
    ) {
      throw new ClinicError(ERROR_TYPE.NOT_FOUND);
    }
    return await this.reservationService.getReserveById(
      loginCustomer,
      reserveId,
    );
  }

  @Query(() => ReservationDetailResponse)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getReserveDetailById(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('reserveDetailId', { type: () => Int, nullable: false })
    reserveDetailId: number,
  ): Promise<ReservationDetailResponse | ReservationDetailError> {
    return await this.reservationService.getReservationDetailById(
      reserveDetailId,
      loginCustomer,
    );
  }

  @Query(() => [CustomerTreatmentType])
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getCustomerTreatmentHistories(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('param', {
      type: () => CustomersTreatmentHistoryInput,
    })
    param: CustomersTreatmentHistoryInput,
  ): Promise<Array<CustomerTreatmentType>> {
    const hospital = await this.hospitalService.getHospital(param.hospitalId);

    if (!hospital) {
      throw new BadRequestException();
    }

    return await this.reservationService.getCustomerTreatmentHistories(
      loginCustomer,
      hospital,
      param,
    );
  }

  @Query(() => [ReservationOrPharmacyReserve])
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getUpComingReservations(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('params', {
      type: () => ReservationDetailsByConditionsInput,
    })
    params: ReservationDetailsByConditionsInput,
  ): Promise<Array<typeof ReservationOrPharmacyReserve>> {
    const familyMembers =
      await this.customerService.getCustomerAndFamilyMemberByCustomerId(
        loginCustomer.customerId,
      );
    const familyMemberIds = new Set(
      familyMembers.map(({ customerId }) => customerId),
    );

    if (params.customerIds.some((id) => !familyMemberIds.has(id))) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: {
          code: ERROR_TYPE.PERMISSION_DENIED,
        },
      });
    }

    const reservationDetails =
      await this.reservationService.getReservationDetailsByConditions(
        params.customerIds,
        RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
      );

    const pharmacyReserves =
      await this.pharmacyService.getPharmacyReservesByConditions(
        params.customerIds,
        RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
      );

    const [combinedResults, cardInfo] = await Promise.all([
      Promise.resolve([...reservationDetails, ...pharmacyReserves]),
      this.fincodeService
        .getCardByCustomerId(loginCustomer.customerId)
        .catch(() => null),
    ]);

    const results = combinedResults
      .map((reservation) => {
        if (cardInfo) {
          reservation.cardExpire = cardInfo.expire;
        }
        return reservation;
      })
      .sort((a, b) => {
        const aSortDate = a.sortDate;
        const bSortDate = b.sortDate;
        return new Date(bSortDate).getTime() - new Date(aSortDate).getTime(); // Sort DESC
      });

    return results;
  }

  @Query(() => [ReservationOrPharmacyReserve])
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async getHistoryReservations(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('params', {
      type: () => ReservationDetailsByConditionsInput,
    })
    params: ReservationDetailsByConditionsInput,
  ): Promise<Array<typeof ReservationOrPharmacyReserve>> {
    const familyMembers =
      await this.customerService.getCustomerAndFamilyMemberByCustomerId(
        loginCustomer.customerId,
      );
    const familyMemberIds = new Set(
      familyMembers.map(({ customerId }) => customerId),
    );

    if (params.customerIds.some((id) => !familyMemberIds.has(id))) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: {
          code: ERROR_TYPE.PERMISSION_DENIED,
        },
      });
    }

    const reservationDetails =
      await this.reservationService.getReservationDetailsByConditions(
        params.customerIds,
        RESERVER_REQUEST_TYPE.HISTORY_RESERVE,
      );

    const pharmacyReserves =
      await this.pharmacyService.getPharmacyReservesByConditions(
        params.customerIds,
        RESERVER_REQUEST_TYPE.HISTORY_RESERVE,
      );

    const combinedResults = [...reservationDetails, ...pharmacyReserves];

    const results = combinedResults.sort((a, b) => {
      const aSortDate = a.sortDate;
      const bSortDate = b.sortDate;
      return new Date(bSortDate).getTime() - new Date(aSortDate).getTime(); // Sort DESC
    });

    return results;
  }

  @Mutation(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async updateTreatmentStatusToCompleted(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('reservationDetailIds', {
      type: () => [Int],
    })
    reservationDetailIds: Array<number>,
  ): Promise<boolean> {
    await this.reservationService.updateTreatmentStatusToCompleted(
      loginCustomer,
      reservationDetailIds,
    );

    return true;
  }

  @Query(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async isExistSameDayReservation(
    @CurrentCustomer() loginCustomer: LoginCustomer,
    @Args('params', { type: () => IsExistSameDayReservationInput })
    params: IsExistSameDayReservationInput,
  ): Promise<boolean> {
    const hasPermission =
      await this.customerService.validateCustomersIsFamilyMember(
        loginCustomer,
        params.customerIds,
      );

    if (!hasPermission) {
      throw new GraphQLError(ERROR_TYPE.PERMISSION_DENIED, {
        extensions: { code: ERROR_TYPE.PERMISSION_DENIED },
      });
    }

    return await this.reservationService.isExistSameDayReservation(
      params.customerIds,
      params.examTimeSlotId,
    );
  }

  @Query(() => Boolean)
  @UseGuards(
    ActiveCustomerLoginGuard,
    ActiveAgreeGuard,
    RegistrationCompletionGuard,
  )
  async isExistPaymentError(
    @CurrentCustomer() loginCustomer: LoginCustomer,
  ): Promise<boolean> {
    return await this.reservationService.isExistPaymentError(
      loginCustomer.customerId,
    );
  }
}
