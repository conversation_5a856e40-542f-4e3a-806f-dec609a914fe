import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Request } from 'express';
import { Transaction } from 'objection';

import {
  HOSPITAL_KARTE_STATUS,
  RESERVER_REQUEST_TYPE,
  TEMPLATE_MAIL_CODE,
} from '@/common/constants';
import {
  IS_DELETED,
  PHARMACY_PRESCRIPTION_RECEIVE_METHOD,
  RESERVATION_STATUS,
} from '@/common/constants/master-type';
import { Logger } from '@/common/logger/logger';
import { MailService } from '@/common/service/mail.service';
import { ClinicError } from '@/common/utils/error';
import { CustomerService } from '@/customer/customer.service';
import { ExamTimeSlotService } from '@/exam-time-slot/exam-time-slot.service';
import { Customer } from '@/models/Customer';
import { CustomerDeliveryAddress } from '@/models/CustomerDeliveryAddress';
import { HospitalInfo } from '@/models/HospitalInfo';
import { Meeting } from '@/models/Meeting';
import { Patient } from '@/models/Patient';
import { PaymentClinicDetail } from '@/models/PaymentClinicDetail';
import { PaymentPharmacyDetail } from '@/models/PaymentPharmacyDetail';
import { PharmacyDesiredDate } from '@/models/PharmacyDesiredDate';
import { PharmacyReserve } from '@/models/PharmacyReserve';
import { PharmacyReserveDetail } from '@/models/PharmacyReserveDetail';
import { PharmacyReserveStatusHistory } from '@/models/PharmacyReserveStatusHistory';
import { PortalCustomerPharmacy } from '@/models/PortalCustomerPharmacy';
import { Reservation } from '@/models/Reservation';
import { ReservationDetail } from '@/models/ReservationDetail';
import { ReservationDetailHistory } from '@/models/ReservationDetailHistory';
import { PatientService } from '@/patient/patient.service';
import PharmacyService from '@/pharmacy/pharmacy.service';
import { PharmacyDeliveryAddressService } from '@/pharmacy-delivery-address/pharmacy-delivery-address.service';
import { SearchHospitalService } from '@/search-hospital/search-hospital.service';
import { SurveyService } from '@/survey/survey.service';

import ReservationService from './reservation.service';

describe('ReservationService', () => {
  let service: ReservationService;
  let mockReservationDetail: jest.Mocked<typeof ReservationDetail>;
  let mockPharmacyReserve: jest.Mocked<typeof PharmacyReserve>;
  let mockPharmacyReserveDetail: jest.Mocked<typeof PharmacyReserveDetail>;
  let mockPortalCustomerPharmacy: jest.Mocked<typeof PortalCustomerPharmacy>;
  let mockReservationDetailHistory: jest.Mocked<
    typeof ReservationDetailHistory
  >;
  let mockPharmacyReserveStatusHistory: jest.Mocked<
    typeof PharmacyReserveStatusHistory
  >;
  let mockMeeting: jest.Mocked<typeof Meeting>;
  let mockHospital: jest.Mocked<typeof HospitalInfo>;
  let mockPharmacyDesiredDate: jest.Mocked<typeof PharmacyDesiredDate>;
  let mockPatient: jest.Mocked<typeof Patient>;
  let mockPaymentClinicDetail: jest.Mocked<typeof PaymentClinicDetail>;
  let mockPaymentPharmacyDetail: jest.Mocked<typeof PaymentPharmacyDetail>;
  let mockCustomerDeliveryAddress: jest.Mocked<typeof CustomerDeliveryAddress>;
  let mockCustomer: jest.Mocked<typeof Customer>;

  let mockReservationModel: any;

  beforeEach(async () => {
    jest.clearAllMocks();

    const mockChain = {
      query: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      findById: jest.fn().mockReturnThis(),
      withGraphFetched: jest.fn().mockReturnThis(),
      modifyGraph: jest.fn().mockReturnThis(),
    };

    mockReservationModel = {
      query: jest.fn().mockReturnValue(mockChain),
      where: jest.fn().mockReturnValue(mockChain),
      findById: jest.fn().mockReturnValue(mockChain),
      withGraphFetched: jest.fn().mockReturnValue(mockChain),
      modifyGraph: jest.fn().mockReturnValue(mockChain),
    };

    mockReservationDetail = {} as any;
    mockPharmacyReserve = {} as any;
    mockPharmacyReserveDetail = {} as any;
    mockPortalCustomerPharmacy = {} as any;
    mockReservationDetailHistory = {} as any;
    mockPharmacyReserveStatusHistory = {} as any;
    mockMeeting = {} as any;
    mockHospital = {} as any;
    mockPharmacyDesiredDate = {} as any;
    mockPatient = {} as any;
    mockPaymentClinicDetail = {} as any;
    mockPaymentPharmacyDetail = {} as any;
    mockCustomerDeliveryAddress = {} as any;
    mockCustomer = {} as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReservationService,
        {
          provide: Reservation.name,
          useValue: mockReservationModel,
        },
        {
          provide: ReservationDetail.name,
          useValue: mockReservationDetail,
        },
        {
          provide: PharmacyReserve.name,
          useValue: mockPharmacyReserve,
        },
        {
          provide: PharmacyReserveDetail.name,
          useValue: mockPharmacyReserveDetail,
        },
        {
          provide: PortalCustomerPharmacy.name,
          useValue: mockPortalCustomerPharmacy,
        },
        {
          provide: ReservationDetailHistory.name,
          useValue: mockReservationDetailHistory,
        },
        {
          provide: PharmacyReserveStatusHistory.name,
          useValue: mockPharmacyReserveStatusHistory,
        },
        {
          provide: Meeting.name,
          useValue: mockMeeting,
        },
        {
          provide: HospitalInfo.name,
          useValue: mockHospital,
        },
        {
          provide: PharmacyDesiredDate.name,
          useValue: mockPharmacyDesiredDate,
        },
        {
          provide: Patient.name,
          useValue: mockPatient,
        },
        {
          provide: PaymentClinicDetail.name,
          useValue: mockPaymentClinicDetail,
        },
        {
          provide: PaymentPharmacyDetail.name,
          useValue: mockPaymentPharmacyDetail,
        },
        {
          provide: CustomerDeliveryAddress.name,
          useValue: mockCustomerDeliveryAddress,
        },
        {
          provide: Customer.name,
          useValue: mockCustomer,
        },
        {
          provide: SurveyService,
          useValue: {},
        },
        {
          provide: PharmacyService,
          useValue: {},
        },
        {
          provide: MailService,
          useValue: {},
        },
        {
          provide: ExamTimeSlotService,
          useValue: {},
        },
        {
          provide: SearchHospitalService,
          useValue: {},
        },
        {
          provide: PharmacyDeliveryAddressService,
          useValue: {},
        },
        {
          provide: PatientService,
          useValue: {},
        },
        {
          provide: CustomerService,
          useValue: {},
        },
        {
          provide: Logger,
          useValue: {},
        },
        {
          provide: require('@/raiin/raiin.service').default,
          useValue: {
            registerRaiinInfo: jest.fn(),
            updateRaiinInfo: jest.fn(),
            deleteRaiinInfo: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ReservationService>(ReservationService);
  });

  describe('findOne', () => {
    it('should return a reservation with all its relations', async () => {
      const mockReservationData = {
        id: 1,
        patient: {
          id: 1,
          name: 'Test Patient',
        },
        pharmacyReserve: {
          id: 1,
          status: 'PENDING',
        },
        reservationDetails: [
          {
            id: 1,
            status: 'PENDING',
          },
        ],
      };

      // Create a mock chain object that will be returned by each method
      const mockChain = {
        query: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        findById: jest.fn().mockReturnThis(),
        withGraphFetched: jest.fn().mockReturnThis(),
        modifyGraph: jest.fn().mockReturnThis(),
      };

      // Set up the mock chain for modifyGraph calls
      mockChain.modifyGraph
        .mockImplementationOnce(() => mockChain) // pharmacyReserve
        .mockImplementationOnce(() => mockChain) // portalCustomerPharmacy
        .mockImplementationOnce(() => mockChain) // reservationDetails
        .mockResolvedValueOnce(mockReservationData); // pharmacyReserve.pharmacyReservationDetails

      // Set up the mock to return our chain
      mockReservationModel.query.mockReturnValue(mockChain);

      const result = await service.findOne(1);

      expect(result).toEqual(mockReservationData);
      expect(mockReservationModel.query).toHaveBeenCalled();
      expect(mockChain.where).toHaveBeenCalledWith(
        'isDeleted',
        IS_DELETED.FALSE,
      );
      expect(mockChain.findById).toHaveBeenCalledWith(1);
      expect(mockChain.withGraphFetched).toHaveBeenCalledWith({
        patient: {
          hospital: true,
        },
        pharmacyReserve: {
          pharmacyReservationDetails: true,
          meetings: true,
        },
        portalCustomerPharmacy: true,
        meeting: true,
        reservationDetails: {
          patient: {
            portalCustomer: true,
          },
          reservation: {
            patient: {
              portalCustomer: true,
            },
          },
          examTimeSlot: true,
          surveyAnswer: true,
          examDetails: true,
          calendarTreatment: {
            treatmentDepartment: {
              treatmentCategory: true,
              hospital: {
                portalHospital: {
                  stations: {
                    stationDetail: {
                      railline: true,
                    },
                  },
                },
                finCodeInfo: true,
              },
            },
          },
          raiinInf: true,
        },
      });
      expect(mockChain.modifyGraph).toHaveBeenCalledTimes(4);
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        1,
        'pharmacyReserve',
        expect.any(Function),
      );
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        2,
        'portalCustomerPharmacy',
        expect.any(Function),
      );
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        3,
        'reservationDetails',
        expect.any(Function),
      );
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        4,
        'pharmacyReserve.pharmacyReservationDetails',
        expect.any(Function),
      );
    });

    it('should return undefined when reservation is not found', async () => {
      // Create a mock chain object that will be returned by each method
      const mockChain = {
        query: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        findById: jest.fn().mockReturnThis(),
        withGraphFetched: jest.fn().mockReturnThis(),
        modifyGraph: jest.fn().mockReturnThis(),
      };

      // Set up the mock chain for modifyGraph calls
      mockChain.modifyGraph
        .mockImplementationOnce(() => mockChain) // pharmacyReserve
        .mockImplementationOnce(() => mockChain) // portalCustomerPharmacy
        .mockImplementationOnce(() => mockChain) // reservationDetails
        .mockResolvedValueOnce(undefined); // pharmacyReserve.pharmacyReservationDetails

      // Set up the mock to return our chain
      mockReservationModel.query.mockReturnValue(mockChain);

      const result = await service.findOne(999);

      expect(result).toBeUndefined();
      expect(mockReservationModel.query).toHaveBeenCalled();
      expect(mockChain.where).toHaveBeenCalledWith(
        'isDeleted',
        IS_DELETED.FALSE,
      );
      expect(mockChain.findById).toHaveBeenCalledWith(999);
      expect(mockChain.withGraphFetched).toHaveBeenCalledWith({
        patient: {
          hospital: true,
        },
        pharmacyReserve: {
          pharmacyReservationDetails: true,
          meetings: true,
        },
        portalCustomerPharmacy: true,
        meeting: true,
        reservationDetails: {
          patient: {
            portalCustomer: true,
          },
          reservation: {
            patient: {
              portalCustomer: true,
            },
          },
          examTimeSlot: true,
          surveyAnswer: true,
          examDetails: true,
          calendarTreatment: {
            treatmentDepartment: {
              treatmentCategory: true,
              hospital: {
                portalHospital: {
                  stations: {
                    stationDetail: {
                      railline: true,
                    },
                  },
                },
                finCodeInfo: true,
              },
            },
          },
          raiinInf: true,
        },
      });
      expect(mockChain.modifyGraph).toHaveBeenCalledTimes(4);
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        1,
        'pharmacyReserve',
        expect.any(Function),
      );
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        2,
        'portalCustomerPharmacy',
        expect.any(Function),
      );
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        3,
        'reservationDetails',
        expect.any(Function),
      );
      expect(mockChain.modifyGraph).toHaveBeenNthCalledWith(
        4,
        'pharmacyReserve.pharmacyReservationDetails',
        expect.any(Function),
      );
    });
  });

  describe('getReservationDetailsByConditions', () => {
    it('should return reservation details for upcoming reservations', async () => {
      const customerIds = [1, 2];
      const mockReserveIds = [100, 101];
      const mockReservationDetails = [
        {
          reserveDetailId: 1,
          sortDate: '2024-03-20',
          status: 'PENDING',
          patient: {
            id: 1,
            portalCustomer: { customerId: 1 },
          },
        },
        {
          reserveDetailId: 2,
          sortDate: '2024-03-21',
          status: 'PENDING',
          patient: {
            id: 2,
            portalCustomer: { customerId: 2 },
          },
        },
      ];

      // Mock getReserveIdsByCondition
      const mockGetReserveIdsByCondition = jest
        .spyOn(service as any, 'getReserveIdsByCondition')
        .mockResolvedValue(mockReserveIds);

      // Mock reservationDetail query chain
      const mockQueryBuilder: any = {};
      [
        'select',
        'joinRelated',
        'withGraphFetched',
        'modifyGraph',
        'where',
        'andWhere',
        'whereIn',
        'orderBy',
        'whereNot',
      ].forEach((fn) => {
        mockQueryBuilder[fn] = jest.fn().mockReturnValue(mockQueryBuilder);
      });
      // Make the chain awaitable (thenable)
      mockQueryBuilder.then = jest.fn((resolve) =>
        resolve(mockReservationDetails),
      );

      mockReservationDetail.query = jest.fn().mockReturnValue(mockQueryBuilder);

      const result = await service.getReservationDetailsByConditions(
        customerIds,
        RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
      );

      expect(mockGetReserveIdsByCondition).toHaveBeenCalledWith(
        customerIds,
        RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
      );
      expect(mockReservationDetail.query).toHaveBeenCalled();
      expect(mockQueryBuilder.select).toHaveBeenCalledWith([
        'examTimeSlot.examStartDate as sortDate',
        'reserveDetail.*',
      ]);
      expect(mockQueryBuilder.joinRelated).toHaveBeenCalledWith(
        '[patient.portalCustomer, examTimeSlot]',
      );
      expect(mockQueryBuilder.whereIn).toHaveBeenCalledWith(
        'patient:portalCustomer.customerId',
        customerIds,
      );
      expect(mockQueryBuilder.whereIn).toHaveBeenCalledWith(
        'reserveId',
        mockReserveIds,
      );
      expect(mockQueryBuilder.whereNot).toHaveBeenCalledWith(
        'reserveDetail.status',
        RESERVATION_STATUS.CANCELED,
      );
      expect(result).toEqual(mockReservationDetails);
    });

    it('should return reservation details for history reservations', async () => {
      const customerIds = [1, 2];
      const mockReserveIds = [100, 101];
      const mockReservationDetails = [
        {
          reserveDetailId: 1,
          sortDate: '2024-03-20',
          status: 'COMPLETED',
          patient: {
            id: 1,
            portalCustomer: { customerId: 1 },
          },
        },
        {
          reserveDetailId: 2,
          sortDate: '2024-03-21',
          status: 'COMPLETED',
          patient: {
            id: 2,
            portalCustomer: { customerId: 2 },
          },
        },
      ];

      // Mock getReserveIdsByCondition
      const mockGetReserveIdsByCondition = jest
        .spyOn(service as any, 'getReserveIdsByCondition')
        .mockResolvedValue(mockReserveIds);

      // Mock reservationDetail query chain
      const mockQueryBuilder: any = {};
      [
        'select',
        'joinRelated',
        'withGraphFetched',
        'where',
        'andWhere',
        'whereIn',
        'orderBy',
        'modifyGraph',
      ].forEach((fn) => {
        mockQueryBuilder[fn] = jest.fn().mockReturnValue(mockQueryBuilder);
      });
      // Make the chain awaitable (thenable)
      mockQueryBuilder.then = jest.fn((resolve) =>
        resolve(mockReservationDetails),
      );

      mockReservationDetail.query = jest.fn().mockReturnValue(mockQueryBuilder);

      const result = await service.getReservationDetailsByConditions(
        customerIds,
        RESERVER_REQUEST_TYPE.HISTORY_RESERVE,
      );

      expect(mockGetReserveIdsByCondition).toHaveBeenCalledWith(
        customerIds,
        RESERVER_REQUEST_TYPE.HISTORY_RESERVE,
      );
      expect(mockReservationDetail.query).toHaveBeenCalled();
      expect(mockQueryBuilder.select).toHaveBeenCalledWith([
        'examTimeSlot.examStartDate as sortDate',
        'reserveDetail.*',
      ]);
      expect(mockQueryBuilder.joinRelated).toHaveBeenCalledWith(
        '[patient.portalCustomer, examTimeSlot]',
      );
      expect(mockQueryBuilder.whereIn).toHaveBeenCalledWith(
        'patient:portalCustomer.customerId',
        customerIds,
      );
      expect(mockQueryBuilder.whereIn).toHaveBeenCalledWith(
        'reserveId',
        mockReserveIds,
      );
      expect(mockQueryBuilder.modifyGraph).toHaveBeenCalledWith(
        'calendarTreatment.treatmentDepartment',
        expect.any(Function),
      );
      expect(mockQueryBuilder.modifyGraph).toHaveBeenCalledWith(
        'examTimeSlot',
        expect.any(Function),
      );
      expect(mockQueryBuilder.modifyGraph).toHaveBeenCalledWith(
        'examTimeSlot.calendar',
        expect.any(Function),
      );
      expect(result).toEqual(mockReservationDetails);
    });

    it('should handle empty reserveIds', async () => {
      const customerIds = [1, 2];
      const mockReserveIds: number[] = [];

      // Mock getReserveIdsByCondition
      const mockGetReserveIdsByCondition = jest
        .spyOn(service as any, 'getReserveIdsByCondition')
        .mockResolvedValue(mockReserveIds);

      // Mock reservationDetail query chain
      const mockQueryBuilder: any = {};
      [
        'select',
        'joinRelated',
        'withGraphFetched',
        'modifyGraph',
        'where',
        'andWhere',
        'whereIn',
        'orderBy',
        'whereNot',
      ].forEach((fn) => {
        mockQueryBuilder[fn] = jest.fn().mockReturnValue(mockQueryBuilder);
      });
      // Make the chain awaitable (thenable)
      mockQueryBuilder.then = jest.fn((resolve) => resolve([]));

      mockReservationDetail.query = jest.fn().mockReturnValue(mockQueryBuilder);

      const result = await service.getReservationDetailsByConditions(
        customerIds,
        RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
      );

      expect(mockGetReserveIdsByCondition).toHaveBeenCalledWith(
        customerIds,
        RESERVER_REQUEST_TYPE.UP_COMING_RESERVE,
      );
      expect(mockReservationDetail.query).toHaveBeenCalled();
      expect(mockQueryBuilder.whereIn).toHaveBeenCalledWith(
        'reserveId',
        mockReserveIds,
      );
      expect(result).toEqual([]);
    });
  });

  describe('getReserveById', () => {
    const mockCustomer = {
      customerId: 1,
      name: 'Test Customer',
    } as Customer;

    const mockFamilyMembers = [
      { customerId: 1, name: 'Test Customer' },
      { customerId: 2, name: 'Family Member 1' },
    ];

    const mockReservationDetails = [
      {
        reserveId: 100,
        reserveDetailId: 1,
        status: 'PENDING',
        patient: {
          portalCustomerId: 1,
          portalCustomer: {
            customerId: 1,
            customerSurvey: {},
          },
        },
        reservation: {
          reserveId: 100,
          pharmacyReserve: {
            pharmacyDeliveryAddress: {},
            pharmacyReservationDetails: [],
          },
          portalCustomerPharmacy: {},
          meeting: {},
        },
        examTimeSlot: {
          calendar: {},
        },
        calendarTreatment: {
          calendar: {},
          treatmentDepartment: {
            firstMedicalInterviewForm: {},
            nextMedicalInterviewForm: {},
            treatmentCategory: {},
            hospital: {
              portalHospital: {
                stations: {
                  stationDetail: {
                    railline: {},
                  },
                },
              },
            },
          },
        },
      },
    ];

    beforeEach(() => {
      // Mock customerService.getCustomerAndFamilyMemberByCustomerId
      const mockCustomerService = {
        getCustomerAndFamilyMemberByCustomerId: jest
          .fn()
          .mockResolvedValue(mockFamilyMembers),
      };
      (service as any).customerService = mockCustomerService;

      // Mock reservationDetail query chain
      const mockQueryBuilder: any = {};
      [
        'query',
        'joinRelated',
        'withGraphJoined',
        'withGraphFetched',
        'whereIn',
        'whereNot',
        'andWhere',
        'modifyGraph',
      ].forEach((fn) => {
        mockQueryBuilder[fn] = jest.fn().mockReturnValue(mockQueryBuilder);
      });
      mockQueryBuilder.then = jest.fn((resolve) =>
        resolve(mockReservationDetails),
      );

      mockReservationDetail.query = jest.fn().mockReturnValue(mockQueryBuilder);
    });

    it('should return reservation details when found', async () => {
      const result = await service.getReserveById(mockCustomer, 100);

      expect(result).toEqual({
        statusCode: HttpStatus.OK,
        reservation: {
          ...mockReservationDetails[0].reservation,
          reserveId: 100,
          reservationDetails: [mockReservationDetails[0]],
        },
      });

      expect(mockReservationDetail.query).toHaveBeenCalled();
      expect(mockReservationDetail.query().joinRelated).toHaveBeenCalledWith(
        '[patient, reservation]',
      );
      expect(
        mockReservationDetail.query().withGraphJoined,
      ).toHaveBeenCalledWith(
        '[reservation.portalCustomerPharmacy, reservation.pharmacyReserve]',
      );
      expect(mockReservationDetail.query().whereIn).toHaveBeenCalledWith(
        'patient.portalCustomerId',
        mockFamilyMembers.map(({ customerId }) => customerId),
      );
      expect(mockReservationDetail.query().whereNot).toHaveBeenCalledWith(
        'status',
        RESERVATION_STATUS.CANCELED,
      );
    });

    it('should throw ClinicError when reservation does not exist', async () => {
      const mockQueryBuilder: any = {};
      [
        'query',
        'joinRelated',
        'withGraphJoined',
        'withGraphFetched',
        'whereIn',
        'whereNot',
        'andWhere',
        'modifyGraph',
      ].forEach((fn) => {
        mockQueryBuilder[fn] = jest.fn().mockReturnValue(mockQueryBuilder);
      });
      mockQueryBuilder.then = jest.fn((resolve) => resolve([]));

      mockReservationDetail.query = jest.fn().mockReturnValue(mockQueryBuilder);

      await expect(service.getReserveById(mockCustomer, 999)).rejects.toThrow(
        ClinicError,
      );
    });

    it('should throw ClinicError when reservation details are empty', async () => {
      const mockQueryBuilder: any = {};
      [
        'query',
        'joinRelated',
        'withGraphJoined',
        'withGraphFetched',
        'whereIn',
        'whereNot',
        'andWhere',
        'modifyGraph',
      ].forEach((fn) => {
        mockQueryBuilder[fn] = jest.fn().mockReturnValue(mockQueryBuilder);
      });
      mockQueryBuilder.then = jest.fn((resolve) => resolve([]));

      mockReservationDetail.query = jest.fn().mockReturnValue(mockQueryBuilder);

      await expect(service.getReserveById(mockCustomer, 100)).rejects.toThrow(
        ClinicError,
      );
    });
  });

  describe('getReservationPharmacyInfo', () => {
    const mockYakkyoku24HospitalInfo = {
      hpName: '薬局24',
      tel: '0312345678',
      faxNo: '0312345679',
      postCd: '1000001',
      address1: '東京都千代田区',
      address2: '1-1-1',
    };

    const mockPortalCustomerPharmacy = {
      pharmacyName: 'Test Pharmacy',
      phoneNumber: '0312345678',
      faxNumber: '0312345679',
      postCode: '1000001',
      address1: '東京都千代田区',
      address2: '1-1-1',
    };

    beforeEach(() => {
      // Mock getYakkyoku24HospitalInfo
      jest
        .spyOn(service as any, 'getYakkyoku24HospitalInfo')
        .mockResolvedValue(mockYakkyoku24HospitalInfo);
    });

    it('should return null when reservation has no pharmacy info', async () => {
      const mockReservationDetail = {
        reservation: {
          prescriptionReceiveMethod:
            PHARMACY_PRESCRIPTION_RECEIVE_METHOD.NOT_SPECIFY,
        },
      } as ReservationDetail;

      const result = await service.getReservationPharmacyInfo(
        mockReservationDetail,
      );

      expect(result).toBeNull();
    });

    it('should return GMO24 pharmacy info when prescriptionReceiveMethod is GMO24_PHARMACY', async () => {
      const mockReservationDetail = {
        reservation: {
          prescriptionReceiveMethod:
            PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY,
          pharmacyReserve: {
            pharmacyDeliveryAddress: {
              address1: '東京都千代田区',
              address2: '1-1-1',
              postCode: '1000001',
              phoneNumber: '0312345678',
            },
          },
        },
      } as ReservationDetail;

      const result = await service.getReservationPharmacyInfo(
        mockReservationDetail,
      );

      expect(result).toEqual({
        prescriptionReceiveMethod: '配送受取',
        pharmacyName: '薬局24',
        pharmacyPhoneNumber: '03-1234-5678',
        pharmacyFaxNumber: '03-1234-5679',
        pharmacyAddress: '〒100-0001 東京都千代田区 1-1-1',
        paymentMethod: 'ご登録クレジットカード',
      });
    });

    it('should return customer specified pharmacy info when prescriptionReceiveMethod is CUSTOMER_SPECIFIED_PHARMACY', async () => {
      const mockReservationDetail = {
        reservation: {
          prescriptionReceiveMethod:
            PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY,
          portalCustomerPharmacy: mockPortalCustomerPharmacy,
        },
      } as ReservationDetail;

      const result = await service.getReservationPharmacyInfo(
        mockReservationDetail,
      );

      expect(result).toEqual({
        prescriptionReceiveMethod: '薬局受取（処方箋を薬局に送信）',
        pharmacyName: 'Test Pharmacy',
        pharmacyPhoneNumber: '03-1234-5678',
        pharmacyFaxNumber: '03-1234-5679',
        pharmacyAddress: '〒100-0001 東京都千代田区 1-1-1',
        paymentMethod: 'ご登録クレジットカード',
      });
    });

    it('should handle missing pharmacy address fields', async () => {
      const mockReservationDetail = {
        reservation: {
          prescriptionReceiveMethod:
            PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY,
          portalCustomerPharmacy: {
            ...mockPortalCustomerPharmacy,
            address2: undefined,
          },
        },
      } as ReservationDetail;

      const result = await service.getReservationPharmacyInfo(
        mockReservationDetail,
      );

      expect(result).toEqual({
        prescriptionReceiveMethod: '薬局受取（処方箋を薬局に送信）',
        pharmacyName: 'Test Pharmacy',
        pharmacyPhoneNumber: '03-1234-5678',
        pharmacyFaxNumber: '03-1234-5679',
        pharmacyAddress: '〒100-0001 東京都千代田区 ',
        paymentMethod: 'ご登録クレジットカード',
      });
    });
  });

  describe('reserve', () => {
    const mockParams = {
      hospitalId: 1,
      calendarTreatmentId: 2,
      examTimeSlotId: 3,
      reserveType: 1,
      reservationMethod: 0,
      customers: [
        { customerId: 10, treatmentType: 1 },
        { customerId: 11, treatmentType: 2 },
      ],
      prescriptionReceiveMethod:
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY,
      pharmacyDeliveryAddress: {
        address1: 'A',
        address2: 'B',
        postCode: '123',
        phoneNumber: '090',
      },
      portalCustomerPharmacy: undefined,
      paymentCardId: 'card123',
      fincodeCustomerId: 'fincode123',
    };
    const mockLoginCustomer = {
      customerId: 99,
      email: '<EMAIL>',
      telephone: '090',
      name: 'Test',
      kanaName: 'テスト',
      gender: 1,
      birthday: '2000-01-01',
    };
    const mockRequest = {} as Request;

    const mockHospitalInfo = {
      hpId: 1,
      finCodeInfo: { hpTenantShopId: 'tenant' },
      karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE, // Added karteStatus
    };
    const mockPatient = { ptId: 100 };
    const mockExamTimeSlot = {
      examStartDate: new Date(),
      examEndDate: new Date(),
      calendar: { hospitalId: 1 },
      reservationDetails: [],
    };
    const mockReservationDetails = [
      {
        reserveDetailId: 1,
        examTimeSlot: mockExamTimeSlot,
        patient: {
          ptId: 100,
          hpId: 1,
          hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
        },
        calendarTreatment: { treatmentDepartmentId: 1 },
        reserveId: 1,
        patientId: 100,
        memo: 'test memo',
      },
      {
        reserveDetailId: 2,
        examTimeSlot: mockExamTimeSlot,
        patient: {
          ptId: 101,
          hpId: 1,
          hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
        },
        calendarTreatment: { treatmentDepartmentId: 2 },
        reserveId: 1,
        patientId: 101,
        memo: 'test memo 2',
      },
    ];
    const mockPharmacyData = { hpId: 2 };
    const mockPharmacyReserve = {
      pharmacyReserve: { pharmacyReservationDetails: [{}, {}] },
    };
    const mockReservation = {
      reservationId: 1,
      reservationDetails: mockReservationDetails,
      pharmacyReserve: { pharmacyReservationDetails: [{}, {}] },
    };
    const mockTrx = { commit: jest.fn(), rollback: jest.fn() };

    beforeEach(() => {
      jest.clearAllMocks();
      // Mock hospital
      service['hospital'] = {
        query: jest.fn().mockReturnThis(),
        joinRelated: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        withGraphFetched: jest.fn().mockReturnThis(),
        first: jest.fn().mockReturnThis(),
        throwIfNotFound: jest.fn().mockResolvedValue(mockHospitalInfo),
      } as any;
      // Mock getPatientByPortalCustomer
      jest
        .spyOn(service as any, 'getPatientByPortalCustomer')
        .mockResolvedValue(mockPatient);
      // Mock examSlotService
      (service as any)['examSlotService'] = {
        getExamSlotById: jest.fn().mockResolvedValue(mockExamTimeSlot),
      } as any;
      // Mock getReservationDetail
      jest.spyOn(service as any, 'getReservationDetail').mockResolvedValue({
        examTimeSlot: mockExamTimeSlot,
      });
      // Mock findHospitalPharmacy
      jest
        .spyOn(service, 'findHospitalPharmacy')
        .mockResolvedValue(mockPharmacyData as any);
      // Mock getPharmacyReserveInfo
      jest
        .spyOn(service as any, 'getPharmacyReserveInfo')
        .mockResolvedValue(mockPharmacyReserve);
      // Mock Reservation.startTransaction
      (Reservation as any).startTransaction = jest
        .fn()
        .mockResolvedValue(mockTrx);
      // Mock reservation.query().insertGraphAndFetch
      service['reservation'] = {
        query: jest.fn().mockReturnThis(),
        insertGraphAndFetch: jest.fn().mockReturnThis(),
        withGraphFetched: jest.fn().mockResolvedValue(mockReservation),
      } as any;
      // Mock pharmacyReserveDetail.query().upsertGraphAndFetch
      service['pharmacyReserveDetail'] = {
        query: jest.fn().mockReturnThis(),
        upsertGraphAndFetch: jest
          .fn()
          .mockResolvedValue([{ reserveDetailId: 1 }, { reserveDetailId: 2 }]),
      } as any;
      // Mock pharmacyService.insertPharmacyStatusHistoriesWhenReservePharmacy
      (service as any)['pharmacyService'] = {
        insertPharmacyStatusHistoriesWhenReservePharmacy: jest.fn(),
      } as any;
      // Mock openSearchService.updateReservableSlots
      (service as any)['openSearchService'] = {
        updateReservableSlots: jest.fn(),
      } as any;
      // Mock mailService.sendMailByMailCode
      (service as any)['mailService'] = {
        sendMailByMailCode: jest.fn(),
      };
      // Mock sendMailNotification, sendMailNotificationToClinic, sendMailNotificationToPharmacy
      jest
        .spyOn(service as any, 'sendMailNotification')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotificationToClinic')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotificationToPharmacy')
        .mockResolvedValue(undefined);
      // Mock raiinService
      (service as any)['raiinService'] = {
        registerRaiinInfo: jest.fn().mockResolvedValue({ errors: [] }),
      } as any;
      // Mock convertReserveDetailToRegisterRaiinInfInput
      jest
        .spyOn(service as any, 'convertReserveDetailToRegisterRaiinInfInput')
        .mockReturnValue({ receptionModel: { hpId: 1, ptId: 100 } });
    });

    it('should create a reservation and send notifications (happy path)', async () => {
      const result = await service.reserve(
        mockParams as any,
        mockLoginCustomer as any,
      );
      expect(result).toBe(mockReservation);
      expect(service['reservation'].query).toHaveBeenCalled();
      expect(
        (service as any)['reservation'].insertGraphAndFetch,
      ).toHaveBeenCalled();
      expect(
        (service as any)['pharmacyReserveDetail'].upsertGraphAndFetch,
      ).toHaveBeenCalled();
      expect(
        service['pharmacyService']
          .insertPharmacyStatusHistoriesWhenReservePharmacy,
      ).toHaveBeenCalled();
      expect(mockTrx.commit).toHaveBeenCalled();
      expect(
        service['openSearchService'].updateReservableSlots,
      ).toHaveBeenCalled();
      expect((service as any).sendMailNotification).toHaveBeenCalled();
      expect((service as any).sendMailNotificationToClinic).toHaveBeenCalled();
      expect(
        (service as any).sendMailNotificationToPharmacy,
      ).toHaveBeenCalled();
      // Verify Raiin info is registered when karteStatus is REAL_IN_USE
      expect(service['raiinService'].registerRaiinInfo).toHaveBeenCalled();
    });

    it('should not register Raiin info when hospital karteStatus is not REAL_IN_USE', async () => {
      // Set hospital karteStatus to something other than REAL_IN_USE
      const modifiedHospitalInfo = {
        ...mockHospitalInfo,
        karteStatus: HOSPITAL_KARTE_STATUS.DEMO_IN_USE,
      };

      service['hospital'] = {
        query: jest.fn().mockReturnThis(),
        joinRelated: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        withGraphFetched: jest.fn().mockReturnThis(),
        first: jest.fn().mockReturnThis(),
        throwIfNotFound: jest.fn().mockResolvedValue(modifiedHospitalInfo),
      } as any;

      await service.reserve(mockParams as any, mockLoginCustomer as any);

      expect(service['raiinService'].registerRaiinInfo).not.toHaveBeenCalled();
    });

    it('should handle errors from registerRaiinInfo', async () => {
      // Setup registerRaiinInfo to return errors
      (
        service['raiinService'].registerRaiinInfo as jest.Mock
      ).mockResolvedValue({
        errors: [{ message: 'Raiin error' }],
      });

      await expect(
        service.reserve(mockParams as any, mockLoginCustomer as any),
      ).rejects.toThrow();

      expect(mockTrx.rollback).toHaveBeenCalled();
    });
  });

  describe('updateReserve', () => {
    const mockParams = {
      reserveId: 1,
      examTimeSlotId: 2,
      paymentCardId: 'card123',
      fincodeCustomerId: 'fincode123',
      prescriptionReceiveMethod:
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY,
      pharmacyDeliveryAddress: {
        address1: 'A',
        address2: 'B',
        postCode: '123',
        phoneNumber: '090',
      },
      portalCustomerPharmacy: undefined,
      customers: [
        { customerId: 10, treatmentType: 1 },
        { customerId: 11, treatmentType: 2 },
      ],
    };
    const mockLoginCustomer = {
      customerId: 99,
      email: '<EMAIL>',
      telephone: '090',
      name: 'Test',
      kanaName: 'テスト',
      gender: 1,
      birthday: '2000-01-01',
    };
    const mockRequest = {} as Request;
    const mockCurrentReservation = {
      reserveId: 1,
      meeting: null,
      reservation: {
        reserveId: 1,
        meeting: null,
        reservationDetails: [
          {
            reserveDetailId: 1,
            patient: { portalCustomerId: 10 },
            raiinInf: null,
          },
          {
            reserveDetailId: 2,
            patient: { portalCustomerId: 11 },
            raiinInf: null,
          },
        ],
        prescriptionReceiveMethod:
          PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY,
        pharmacyReserve: {
          pharmacyReserveId: 1,
          pharmacyReservationDetails: [{ status: 0 }],
        },
        portalCustomerPharmacy: null,
      },
      reservationDetails: [
        {
          reserveDetailId: 1,
          patient: {
            portalCustomerId: 10,
            hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
          },
          raiinInf: null,
        },
        {
          reserveDetailId: 2,
          patient: {
            portalCustomerId: 11,
            hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
          },
          raiinInf: null,
        },
      ],
      prescriptionReceiveMethod:
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.CUSTOMER_SPECIFIED_PHARMACY,
      pharmacyReserve: {
        pharmacyReserveId: 1,
        pharmacyReservationDetails: [{ status: 0 }],
      },
      portalCustomerPharmacy: null,
      patient: {
        hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
      },
    };
    const mockPharmacyData = { hpId: 2 };
    const mockPatient = { ptId: 100 };
    const mockTrx = {};

    beforeEach(() => {
      jest.clearAllMocks();
      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockCurrentReservation as any);
      jest
        .spyOn(service, 'findHospitalPharmacy')
        .mockResolvedValue(mockPharmacyData as any);
      jest
        .spyOn(service as any, 'getPatientByPortalCustomer')
        .mockResolvedValue(mockPatient);
      jest
        .spyOn(service as any, 'updateReservation')
        .mockResolvedValue(undefined);
      jest.spyOn(service as any, 'updateReservationDetails').mockResolvedValue([
        {
          reserveDetailId: 1,
          patient: { portalCustomerId: 10 },
          raiinInf: null,
        },
        {
          reserveDetailId: 2,
          patient: { portalCustomerId: 11 },
          raiinInf: null,
        },
      ]);
      jest
        .spyOn(service as any, 'updatePharmacyAddress')
        .mockResolvedValue(undefined);
      jest.spyOn(service as any, 'cancelCustomers').mockResolvedValue([]);
      jest
        .spyOn(service as any, 'sendMailNotification')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotificationToClinic')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotificationToPharmacy')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'convertReserveDetailToUpdateRaiinInfoInput')
        .mockReturnValue({ reserveDetailIds: [1, 2] });

      // Mock raiinService
      (service as any)['raiinService'] = {
        updateRaiinInfo: jest.fn().mockResolvedValue({ errors: [] }),
      };

      // Mock execWithTx to call the callback with mockTrx
      jest
        .spyOn(require('@/common/utils/transaction'), 'execWithTx')
        .mockImplementation((async (cb: (trx: any) => Promise<any>) => {
          return cb(mockTrx as any);
        }) as any);
    });

    it('should update reservation and send notifications (happy path)', async () => {
      await expect(
        service.updateReserve(mockParams as any, mockLoginCustomer as any),
      ).resolves.toBeUndefined();
      expect(service.findOne).toHaveBeenCalledWith(1);
      expect(service.findHospitalPharmacy).toHaveBeenCalled();
      expect((service as any).getPatientByPortalCustomer).toHaveBeenCalled();
      expect((service as any).updateReservation).toHaveBeenCalled();
      expect((service as any).updateReservationDetails).toHaveBeenCalled();
      expect((service as any).updatePharmacyAddress).toHaveBeenCalled();
      expect((service as any).cancelCustomers).toHaveBeenCalled();
      expect((service as any).sendMailNotification).toHaveBeenCalled();
      expect((service as any).sendMailNotificationToClinic).toHaveBeenCalled();
      expect(
        (service as any).sendMailNotificationToPharmacy,
      ).toHaveBeenCalled();
      // Verify Raiin info is updated
      expect(service['raiinService'].updateRaiinInfo).toHaveBeenCalled();
    });

    it('should not update Raiin info when hospital karteStatus is not REAL_IN_USE', async () => {
      const mockNonRealReservation = {
        ...mockCurrentReservation,
        patient: {
          hospital: { karteStatus: HOSPITAL_KARTE_STATUS.DEMO_IN_USE },
        },
        reservationDetails: mockCurrentReservation.reservationDetails.map(
          (detail) => ({
            ...detail,
            patient: {
              ...detail.patient,
              hospital: { karteStatus: HOSPITAL_KARTE_STATUS.DEMO_IN_USE },
            },
          }),
        ),
      };

      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockNonRealReservation as any);

      await service.updateReserve(mockParams as any, mockLoginCustomer as any);

      expect(service['raiinService'].updateRaiinInfo).not.toHaveBeenCalled();
    });

    it('should handle errors from updateRaiinInfo', async () => {
      // Setup updateRaiinInfo to return errors
      (service['raiinService'].updateRaiinInfo as jest.Mock).mockResolvedValue({
        errors: [{ message: 'Raiin update error' }],
      });

      await expect(
        service.updateReserve(mockParams as any, mockLoginCustomer as any),
      ).rejects.toThrow();
    });
  });

  describe('cancelReserve', () => {
    const mockLoginCustomer = {
      customerId: 99,
      email: '<EMAIL>',
      telephone: '090',
      name: 'Test',
      kanaName: 'テスト',
      gender: 1,
      birthday: '2000-01-01',
    };
    const mockRequest = {} as Request;

    const mockReservation = {
      reserveId: 1,
      meeting: null,
      reservationDetails: [
        {
          reserveDetailId: 1,
          patient: {
            portalCustomerId: 10,
            hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
          },
          raiinInf: { hpId: 1, raiinNo: 100, sinDate: 20240320, ptId: 100 },
        },
        {
          reserveDetailId: 2,
          patient: {
            portalCustomerId: 11,
            hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
          },
          raiinInf: { hpId: 1, raiinNo: 101, sinDate: 20240320, ptId: 101 },
        },
      ],
      prescriptionReceiveMethod:
        PHARMACY_PRESCRIPTION_RECEIVE_METHOD.GMO24_PHARMACY,
      pharmacyReserve: {
        pharmacyReserveId: 1,
        pharmacyReservationDetails: [
          { pharmacyReserveDetailId: 101, reserveDetailId: 1 },
          { pharmacyReserveDetailId: 102, reserveDetailId: 2 },
        ],
        desiredDateStatus: 0,
      },
      portalCustomerPharmacy: null,
      patient: {
        hospital: { karteStatus: HOSPITAL_KARTE_STATUS.REAL_IN_USE },
      },
    };

    const mockTrx = {};

    beforeEach(() => {
      jest.clearAllMocks();
      jest.spyOn(service, 'findOne').mockResolvedValue(mockReservation as any);
      jest
        .spyOn(service as any, 'cancelPharmacyReserveDetailByIds')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'insertPharmacyStatusHistoriesAfterCanceled')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'updateDesiredDateWhenCancel')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'cancelReserveDtls')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'insertReserveDtlHistoriesAfterCanceled')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotification')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotificationToClinic')
        .mockResolvedValue(undefined);
      jest
        .spyOn(service as any, 'sendMailNotificationToPharmacy')
        .mockResolvedValue(undefined);

      // Fix the mock implementation typing
      jest
        .spyOn(service as any, 'convertReserveDetailToDeleteRaiinInfInput')
        .mockImplementation((detail: any) => {
          if (!detail?.raiinInf) return null;
          return {
            hpId: detail.raiinInf.hpId,
            cellName: 'Status',
            cellValue: '9',
            raiinNo: detail.raiinInf.raiinNo,
            sinDate: detail.raiinInf.sinDate,
            ptId: detail.raiinInf.ptId,
          };
        });

      // Mock raiinService
      (service as any)['raiinService'] = {
        deleteRaiinInfo: jest.fn().mockResolvedValue({ errors: [] }),
      };

      // Mock execWithTx to call the callback with mockTrx
      jest
        .spyOn(require('@/common/utils/transaction'), 'execWithTx')
        .mockImplementation((async (cb: (trx: Transaction) => Promise<any>) => {
          return cb(mockTrx as any);
        }) as any);

      // Mock examSlotService and openSearchService
      (service as any)['examSlotService'] = {
        getExamSlotById: jest.fn().mockResolvedValue({
          examTimeSlotId: 1,
          reservableSlots: 5,
          calendar: { hospitalId: 1 },
        }),
      };
      (service as any)['openSearchService'] = {
        updateReservableSlots: jest.fn().mockResolvedValue(undefined),
      };
    });

    it('should successfully cancel a reservation with GMO24 pharmacy (happy path)', async () => {
      const result = await service.cancelReserve(
        mockLoginCustomer as any,
        1,
        [1, 2],
      );

      expect(result).toEqual({
        statusCode: HttpStatus.OK,
        reservations: mockReservation,
      });

      expect(service.findOne).toHaveBeenCalledWith(1);
      expect(
        (service as any).cancelPharmacyReserveDetailByIds,
      ).toHaveBeenCalledWith(mockTrx, [101, 102]);
      expect(
        (service as any).insertPharmacyStatusHistoriesAfterCanceled,
      ).toHaveBeenCalledWith(mockTrx, [101, 102]);
      expect((service as any).cancelReserveDtls).toHaveBeenCalledWith(
        mockTrx,
        mockReservation.reservationDetails,
        true,
        undefined,
      );
      expect(
        (service as any).insertReserveDtlHistoriesAfterCanceled,
      ).toHaveBeenCalledWith(mockTrx, mockReservation.reservationDetails);
      expect((service as any).sendMailNotification).toHaveBeenCalledWith(
        mockLoginCustomer.email,
        [1, 2],
        TEMPLATE_MAIL_CODE.CANCEL_RESERVATION,
      );
      expect(
        (service as any).sendMailNotificationToClinic,
      ).toHaveBeenCalledWith(
        [1, 2],
        TEMPLATE_MAIL_CODE.CANCEL_RESERVATION_CLINIC,
      );
      expect(
        (service as any).sendMailNotificationToPharmacy,
      ).toHaveBeenCalledWith(
        [1, 2],
        TEMPLATE_MAIL_CODE.TO_PHARMACY_FOR_CANCEL_RESERVE,
      );

      // Verify Raiin info is deleted
      expect(service['raiinService'].deleteRaiinInfo).toHaveBeenCalledTimes(2);
    });

    it('should not delete Raiin info when hospital karteStatus is not REAL_IN_USE', async () => {
      const mockNonRealReservation = {
        ...mockReservation,
        patient: {
          hospital: { karteStatus: HOSPITAL_KARTE_STATUS.DEMO_IN_USE },
        },
        reservationDetails: mockReservation.reservationDetails.map(
          (detail) => ({
            ...detail,
            patient: {
              ...detail.patient,
              hospital: { karteStatus: HOSPITAL_KARTE_STATUS.DEMO_IN_USE },
            },
          }),
        ),
      };

      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(mockNonRealReservation as any);

      await service.cancelReserve(mockLoginCustomer as any, 1, [1, 2]);

      expect(service['raiinService'].deleteRaiinInfo).not.toHaveBeenCalled();
    });

    it('should handle errors from deleteRaiinInfo', async () => {
      // Setup deleteRaiinInfo to return errors
      (service['raiinService'].deleteRaiinInfo as jest.Mock).mockResolvedValue({
        errors: [{ message: 'Raiin delete error' }],
      });

      await expect(
        service.cancelReserve(mockLoginCustomer as any, 1, [1, 2]),
      ).rejects.toThrow();
    });

    // Add back original tests that were removed
    it('should throw ClinicError when reservation is not found', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValue(undefined);

      await expect(
        service.cancelReserve(mockLoginCustomer as any, 999, [1, 2]),
      ).rejects.toThrow(ClinicError);
    });

    it('should throw error when meeting has already started', async () => {
      const reservationWithMeeting = {
        ...mockReservation,
        meeting: { isBothJoined: true },
      };
      jest
        .spyOn(service, 'findOne')
        .mockResolvedValue(reservationWithMeeting as any);

      await expect(
        service.cancelReserve(mockLoginCustomer as any, 1, [1, 2]),
      ).rejects.toThrow(ClinicError);
    });

    it('should handle partial cancellation of reservation details', async () => {
      await service.cancelReserve(mockLoginCustomer as any, 1, [1]);

      expect((service as any).cancelReserveDtls).toHaveBeenCalledWith(
        mockTrx,
        [mockReservation.reservationDetails[0]],
        false,
        undefined,
      );
      expect(
        (service as any).cancelPharmacyReserveDetailByIds,
      ).toHaveBeenCalledWith(mockTrx, [101]);
    });

    it('should not send mail notifications when isNotSendMail is true', async () => {
      await service.cancelReserve(
        mockLoginCustomer as any,
        1,
        [1, 2],
        undefined,
        true,
      );

      expect((service as any).sendMailNotification).not.toHaveBeenCalled();
      expect(
        (service as any).sendMailNotificationToClinic,
      ).not.toHaveBeenCalled();
      expect(
        (service as any).sendMailNotificationToPharmacy,
      ).not.toHaveBeenCalled();
    });
  });

  describe('sendMailNotification', () => {
    const mockEmailAddress = '<EMAIL>';
    const mockReservationIds = [1, 2];
    const mockReservationInfos = [
      {
        reserveDetailId: 1,
        patientName: 'Test Patient 1',
        examStartDate: '2024-03-20 10:00',
        hospitalName: 'Test Hospital',
        treatmentDepartmentName: 'Test Department',
        treatmentCategoryName: 'Test Category',
        prescriptionReceiveMethod: '配送受取',
        pharmacyName: 'Test Pharmacy',
        pharmacyPhoneNumber: '03-1234-5678',
        pharmacyFaxNumber: '03-1234-5679',
        pharmacyAddress: '〒100-0001 東京都千代田区 1-1-1',
        paymentMethod: 'ご登録クレジットカード',
      },
      {
        reserveDetailId: 2,
        patientName: 'Test Patient 2',
        examStartDate: '2024-03-21 11:00',
        hospitalName: 'Test Hospital',
        treatmentDepartmentName: 'Test Department',
        treatmentCategoryName: 'Test Category',
        prescriptionReceiveMethod: '配送受取',
        pharmacyName: 'Test Pharmacy',
        pharmacyPhoneNumber: '03-1234-5678',
        pharmacyFaxNumber: '03-1234-5679',
        pharmacyAddress: '〒100-0001 東京都千代田区 1-1-1',
        paymentMethod: 'ご登録クレジットカード',
      },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      // Mock logger
      (service as any).logger = {
        error: jest.fn(),
      };
      // Mock getReservationInfos to return different values based on input
      jest
        .spyOn(service as any, 'getReservationInfos')
        .mockImplementation((...args: unknown[]) => {
          const ids = args[0] as number[];
          if (ids.length === 0) return Promise.resolve([]);
          return Promise.resolve(mockReservationInfos);
        });
      // Mock buildMailValues
      jest
        .spyOn(service as any, 'buildMailValues')
        .mockImplementation((...args: unknown[]) => {
          const [infos, isNew] = args as [typeof mockReservationInfos, boolean];
          return {
            patientName: infos[0]?.patientName || '',
            examStartDate: infos[0]?.examStartDate || '',
            hospitalName: infos[0]?.hospitalName || '',
            treatmentDepartmentName: infos[0]?.treatmentDepartmentName || '',
            treatmentCategoryName: infos[0]?.treatmentCategoryName || '',
            prescriptionReceiveMethod:
              infos[0]?.prescriptionReceiveMethod || '',
            pharmacyName: infos[0]?.pharmacyName || '',
            pharmacyPhoneNumber: infos[0]?.pharmacyPhoneNumber || '',
            pharmacyFaxNumber: infos[0]?.pharmacyFaxNumber || '',
            pharmacyAddress: infos[0]?.pharmacyAddress || '',
            paymentMethod: infos[0]?.paymentMethod || '',
          };
        });
      // Mock mailService
      (service as any)['mailService'] = {
        sendMailByMailCode: jest.fn().mockResolvedValue(undefined),
      };
    });

    it('should send mail notification for new reservation', async () => {
      await service.sendMailNotification(
        mockEmailAddress,
        mockReservationIds,
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE,
        true,
      );

      expect((service as any).getReservationInfos).toHaveBeenCalledWith(
        mockReservationIds,
      );
      expect((service as any).buildMailValues).toHaveBeenCalledWith(
        mockReservationInfos,
        true,
      );
      expect(
        (service as any).mailService.sendMailByMailCode,
      ).toHaveBeenCalledWith(
        mockEmailAddress,
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE,
        expect.any(Object),
      );
    });

    it('should send mail notification for existing reservation', async () => {
      await service.sendMailNotification(
        mockEmailAddress,
        mockReservationIds,
        TEMPLATE_MAIL_CODE.UPDATE_RESERVATION_ONLINE,
        false,
      );

      expect((service as any).getReservationInfos).toHaveBeenCalledWith(
        mockReservationIds,
      );
      expect((service as any).buildMailValues).toHaveBeenCalledWith(
        mockReservationInfos,
        false,
      );
      expect(
        (service as any).mailService.sendMailByMailCode,
      ).toHaveBeenCalledWith(
        mockEmailAddress,
        TEMPLATE_MAIL_CODE.UPDATE_RESERVATION_ONLINE,
        expect.any(Object),
      );
    });

    it('should handle empty reservation IDs', async () => {
      await service.sendMailNotification(
        mockEmailAddress,
        [],
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE,
        true,
      );

      expect((service as any).getReservationInfos).toHaveBeenCalledWith([]);
      expect((service as any).buildMailValues).not.toHaveBeenCalled();
      expect(
        (service as any).mailService.sendMailByMailCode,
      ).not.toHaveBeenCalled();
    });

    it('should handle error from getReservationInfos', async () => {
      const error = new Error('Failed to get reservation info');
      jest
        .spyOn(service as any, 'getReservationInfos')
        .mockRejectedValue(error);

      await service.sendMailNotification(
        mockEmailAddress,
        mockReservationIds,
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE,
      );

      expect((service as any).getReservationInfos).toHaveBeenCalledWith(
        mockReservationIds,
      );
      expect((service as any).buildMailValues).not.toHaveBeenCalled();
      expect(
        (service as any).mailService.sendMailByMailCode,
      ).not.toHaveBeenCalled();
      expect((service as any).logger.error).toHaveBeenCalledWith(
        'Error sending email notice to patient: ',
        error,
      );
    });

    it('should handle error from mailService', async () => {
      const error = new Error('Failed to send mail');
      (service as any)['mailService'].sendMailByMailCode.mockRejectedValue(
        error,
      );

      await service.sendMailNotification(
        mockEmailAddress,
        mockReservationIds,
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE,
        false,
      );

      expect((service as any).getReservationInfos).toHaveBeenCalledWith(
        mockReservationIds,
      );
      expect((service as any).buildMailValues).toHaveBeenCalledWith(
        mockReservationInfos,
        false,
      );
      expect(
        (service as any).mailService.sendMailByMailCode,
      ).toHaveBeenCalledWith(
        mockEmailAddress,
        TEMPLATE_MAIL_CODE.NEW_RESERVATION_ONLINE,
        expect.any(Object),
      );
      expect((service as any).logger.error).toHaveBeenCalledWith(
        'Error sending email notice to patient: ',
        error,
      );
    });
  });
});
