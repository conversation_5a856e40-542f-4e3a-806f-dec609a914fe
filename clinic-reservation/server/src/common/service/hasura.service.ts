import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';

import { INTERNAL_API_KEY_HEADER } from '../constants';
import { ERROR_TYPE } from '../constants/error-type';
import { Logger } from '../logger/logger';
import { ClinicError } from '../utils/error';

// HasuraResponse Hasuraからのレスポンス
export type HasuraResponse = {
  data: JSON;
  errors: HasuraError[];
};

// HasuraError Hasuraからのエラー
export type HasuraError = {
  message: string;
  extensions: HasuraErrorExtensions;
};

// HasuraErrorExtensions Hasuraからのエラーの拡張
type HasuraErrorExtensions = {
  code: string;
  userMessage: string;
};

@Injectable()
export default class HasuraService {
  constructor(
    private readonly httpService: HttpService,
    private readonly logger: Logger,
  ) {}

  async sendHasuraRequest<T>(
    query: string,
    variables: T,
  ): Promise<HasuraResponse> {
    try {
      const response = await lastValueFrom(
        this.httpService.post(
          process.env.URL_BASE_SERVER_GRAPHQL_HASURA ||
            'http://localhost:8090/v1/graphql',
          {
            query,
            variables,
          },
          {
            headers: {
              'Content-Type': 'application/json; charset=utf-8',
              [INTERNAL_API_KEY_HEADER]: process.env.INTERNAL_API_KEY,
            },
            withCredentials: true,
          },
        ),
      );

      return response.data as HasuraResponse;
    } catch (error) {
      this.logger.error('Error sending Hasura request: ', error);
      throw new ClinicError(
        'Hasura request failed.',
        ERROR_TYPE.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
