import { DESIRED_TYPE } from './master-type';

// LINE webhook path
export const LINE_WEBHOOK_PATH = 'line/webhook';

// Redis
export const REDIS_TTL = 60 * 10; // TTL in 10 minutes
export const COOKIES_TTL = 60 * 60 * 1000; // Cookies in 60 minutes
export const SESSION_TTL = 60 * 60; // アイソル基準のセッションタイムアウト 60mins
export const REFRESH_TOKEN_TTL = 60 * 60 * 24; // アイソル基準のリフレッシュトークン期限 24hours
export const REFRESH_TOKEN_STAY_LOGGED_IN_TTL = 60 * 60 * 24 * 30; // ログイン状態保持時、アイソル基準のリフレッシュトークン期限 30days

// Encryption Salt
export const USER_REGISTER_EMAIL_PASSWORD = 'register_email_password';
export const USER_REGISTER_EMAIL_SALT = 'register_email_salt';
export const UPDATE_CUSTOMER_EMAIL_PASSWORD = 'update_customer_email_password';
export const UPDATE_CUSTOMER_EMAIL_SALT = 'update_customer_email_salt';

//Error Message
// TODO:メーせじの文言を決める
export enum CUSTOMER_ERROR_MSG {
  PERMISSION_DENIED = '許可が下りない。',
  BIRTHDAY_INPUT_FORMAT = '生年月日の形式が違います。',
}

export enum MEETING_ERROR_MSG {
  MEETING_SESSION_TIMEOUT = 'URLの有効期間が切れています。',
}

export enum DELIVERY_ADDRESS_MSG {
  HOME_ADDRESS_NOT_FOUND = '自宅が見つかりません。',
}

export enum LINE_CHAT_MSG {
  LINK = 'アカウント連携',
  UNLINK = 'アカウント連携削除',
}

export enum LINE_MSG {
  LINE_LINK_SUCCESSFUL = 'LINE連携成功しました。',
}

export enum LINE_EVENT_TYPE {
  FOLLOW = 'follow',
  UNFOLLOW = 'unfollow',
  MESSAGE = 'message',
  POSTBACK = 'postback',
}

export enum LINE_ACTION_TYPE {
  LINKING = 'linking',
  UNLINKING = 'unlinking',
}

export enum STAY_LOGGED_IN {
  TRUE = '1',
  FALSE = '0',
}

export const BIRTHDAY_INPUT_FORMAT = 'YYYYMMDD';

export const SQL_DATE_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export enum RESERVATION_ERROR_MSG {
  RESERVATION_NOT_FOUND = '予約は存在しない。',
  RESERVATION_UPDATE_FAILED = '予約更新に失敗しました。',
  PHARMACY_RESERVATION_DETAIL_NOT_FOUND = '服薬指導予約は存在しない。',
  PHARMACY_RESERVATION_DETAIL_UPDATE_FAILED = '服薬指導予約更新に失敗しました。',
}

export enum RESERVATION_ERR {
  DUPLICATED = '受診する方の中でこの日間で別の医師に予約したので、履歴予約を再確認してください。',
  OUT_OF_SLOT = '1日の予約数が上限に達しました。別の日を選択してください。',
  HISTORY_RESERVED = 'この日間で予約したので、履歴予約を再確認してください。',
  OUT_OF_TIME = '過去で予約できません。再確認してください。',
  RE_SETTING_TIME_SLOT = '予約可能時間が更新されました。 予約時間をもう一度選択してください。',
  PHARMACY_DELIVERY_ADDRESS_REQUIRED = '薬局の住所が必要です。',
  EXIST_PAYMENT_ERROR = '決済エラーが発生しているため、新たなご予約を承れません。予約一覧にて決済内容をご確認ください。',
  RECEPTION_CONNECTED = '受付済のため変更できない',
}

export const MODEL_DEFAULT_VALUE = {
  createdBy: 'Customer',
  updatedBy: 'Customer',
};

// one day from now
export const USER_REGISTER_EMAIL_EXPIRE_SECONDS = 86400;

// 15 mins
export const RESET_PASSWORD_URL_EXPIRE_SECONDS = 900;

// 15 mins
export const UPDATE_EMAIL_URL_EXPIRE_SECONDS = 900;

export const EXAM_TIME_INTEGRATION_ID = 1000000;

export const HOSPITAL_SEARCH_LIMIT = 100;

export const enum RESERVER_REQUEST_TYPE {
  UP_COMING_RESERVE,
  HISTORY_RESERVE,
}

export const MAXIMUM_DESIRED_DATE = 4;

export const MAX_PHARMACY_RESERVE_COUNT = 4;

export const MORNING_MAX_RESERVE_COUNT = 2;

export const DESIRED_TIME_RANGE_END = 21;

export const DESIRED_TIME_RANGE_END_WEEKEND_END = 19;

export const DESIRED_TIME_RANGE = [
  {
    start: 0,
    end: DESIRED_TIME_RANGE_END,
    type: DESIRED_TYPE.UNSPECIFIED,
  },
  {
    start: 10,
    end: 11,
    type: DESIRED_TYPE.MORNING,
  },
  {
    start: 11,
    end: 12,
    type: DESIRED_TYPE.MORNING,
  },
  {
    start: 12,
    end: 13,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 13,
    end: 14,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 14,
    end: 15,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 15,
    end: 16,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 16,
    end: 17,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 17,
    end: 18,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 18,
    end: 19,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 19,
    end: 20,
    type: DESIRED_TYPE.SPECIFY,
  },
  {
    start: 20,
    end: 21,
    type: DESIRED_TYPE.SPECIFY,
  },
];

export const S3_UPLOAD_URL_EXPIRE = 60 * 360;

export enum COOKIES_KEY {
  ACCESS_TOKEN = '_denkaru_accessToken',
  REFRESH_TOKEN = '_denkaru_refreshToken',
  ACCESS_TOKEN_EXPIRE = '_expires',
  STAY_LOGGED_IN = '_stayLoggedIn',
}

export const REGEX_TEMPLATE_LINE_KEY = /^(.*{{\.\s*(\w+)\s*}}.*\n?)/gm;
export const REGEX_TEMPLATE_KEY = /{{\.\s*\w+\s*}}/g;
export const REGEX_TEMPLATE_IF_KEY = /{{if\s+\.(\w+)}}([\s\S]*?){{end}}/g;

export const TEMPLATE_SMS_CODE = {
  SMSOTP: 'SMSOTP',
};

export const TEMPLATE_SMS_KEY = {
  OTP_VALUE: 'OTPValue',
};

export const TEMPLATE_MAIL_CODE = {
  SIGN_UP: 'PortalSignUp',
  SIGN_UP_MAIL_EXISTS: 'PortalSignUpMailExists',
  UPDATE_MAIL: 'PortalChangeEmailConfirmation',
  UPDATE_MAIL_COMPLETED: 'PortalChangeEmailCompleted',
  UPDATE_PASSWORD_COMPLETED: 'PortalChangePasswordCompleted',
  RESET_PASSWORD: 'PortalResetPassword',
  REGISTRATION_COMPLETED: 'PortalRegistrationCompleted',
  CHANGE_CUSTOMER_INFO: 'PortalChangeCustomerInfo',
  WITHDRAW: 'PortalWithdraw',
  NEW_MESSAGE: 'PortalNewMessage',
  CANCEL_RESERVATION: 'PortalCancelReservation',
  NEW_RESERVATION_IN_PERSON: 'PortalNewReservationInPerson',
  NEW_RESERVATION_ONLINE: 'PortalNewReservationOnline',
  UPDATE_RESERVATION_IN_PERSON: 'PortalUpdateReservationInPerson',
  UPDATE_RESERVATION_ONLINE: 'PortalUpdateReservationOnline',
  NEW_RESERVATION_CLINIC: 'NewReservation', // 新規予約のお知らせ
  CANCEL_RESERVATION_CLINIC: 'CancelReservation', // 予約キャンセルのお知らせ
  UPDATE_RESERVATION_CLINIC: 'UpdateReservation', // 予約変更のお知らせ
  LOCKED_ACCOUNT: 'PortalLockedAccount',
  NEW_PHARMACY_RESERVATION: 'PortalPharmacyReserveUpdate',
  UPDATE_PHARMACY_RESERVATION: 'PortalPharmacyReserveUpdate',
  CANCEL_PHARMACY_RESERVATION: 'PortalPharmacyReserveCancel',
  TO_PHARMACY_FOR_RESERVE: 'ToPharmacyForReserve',
  TO_PHARMACY_FOR_CANCEL_RESERVE: 'ToPharmacyForCancelReserve',
};

export const TEMPLATE_MAIL_KEY = {
  SERVICE_NAME: 'ServiceName',
  SIGN_UP_LINK: 'SignUpLink',
  CHANGE_MAIL_URL: 'ChangeEmailUrl',
  RESET_PASSWORD_URL: 'ResetPasswordUrl',
  SIGN_UP_URL_EXPIRE_TIME: 'SignUpUrlExpireTime',
  CHANGE_PASSWORD_URL_EXPIRE_TIME: 'ChangePasswordUrlExpireTime',
  CHANGE_MAIL_URL_EXPIRE_TIME: 'ChangeEmailUrlExpireTime',
  SUPPORT_URL: 'SupportUrl',
  HOMEPAGE_URL: 'HomePageUrl',
  LOGIN_ID: 'LoginId',
  LOGIN_URL: 'LoginUrl',
  FORGET_PASSWORD_URL: 'ForgetPasswordUrl',
  MESSAGE_LINK: 'MessageLink',
  RESERVATION_TIME: 'ReservationTime',
  CLINIC_NAME: 'ClinicName',
  CLINIC_ADDRESS: 'ClinicAddress',
  CLINIC_PHONE_NUMBER: 'ClinicPhoneNumber',
  CLINIC_PAYMENT: 'ClinicPayment',
  RESERVATION_TYPE: 'ReservationType',
  TREATMENT_DEPARTMENT: 'TreatmentDepartment',
  PATIENTS_NAME: 'PatientsName',
  RESERVATION_LIST_URL: 'ReservationListUrl',
  SURVEY_LINK: 'SurveyLink',
  PRESCRIPTION_RECEIVE_METHOD: 'PrescriptionReceiveMethod',
  PHARMACY_NAME: 'PharmacyName',
  PHARMACY_PHONE_NUMBER: 'PharmacyPhoneNumber',
  PHARMACY_FAX_NUMBER: 'PharmacyFaxNumber',
  PHARMACY_ADDRESS: 'PharmacyAddress',
  PHARMACY_PAYMENT: 'PharmacyPayment',
  EXAM_TIME_PERIOD: 'ExamTimePeriod',
  TREATMENT_AND_RESERVE: 'TreatmentAndReserve',
  CALENDAR_LABEL: 'CalendarLabel',
  PASSWORD_RECOVERY: 'PasswordRecovery',
  DESIRED_DATE: 'DesiredDate',
  ALL_PATIENT_NAME: 'AllPatientName',
  HAS_PHARMACY: 'HasPharmacy',
};

export const SERVICE_NAME = 'GMOクリニック・マップ';

export const enum MEETING_ATTENDEE_TYPE {
  PATIENT,
  CLINIC,
}

// 5 mins
export const LINE_LINK_TOKEN_EXPIRED = 300;

// import data
export const SUPPORTED_LANGUAGE = ['英語', '中国語', 'ハングル語'];

export const DEDAULT_SEARCH_RADIUS = 800;

// exam_time_period format
export const EXAM_TIME_PERIOD_FORMAT = 'YYYY年MM月DD日（ddd）HH:mm';

// Date only format
export const DATE_ONLY_FORMAT = 'YYYY年MM月DD日（ddd）';

export const LOG_SENSITIVE_SUB_STRINGS = [
  'username',
  'email',
  'pwd',
  'password',
  'credential',
  'otp',
  'token',
  'card',
  'cvv',
  'passport',
  'auth',
  'session',
  'key',
  'address',
  'zip',
  'postcode',
  'secret',
];

export const FAILED_LOGIN_COUNT = 5;

export const AID = 'aid';
export const BID = 'bid';
export const CID = 'cid';

// Zendesk API の「お知らせ」セクションのデータを取得するためのURL。
export const ZENDESK_NOTIFICATIONS_API_URL =
  'https://support.gmo-clinic-map.com/api/v2/help_center/ja/categories/**************/articles.json?sort_by=edited_at&sort_order=desc';

// 再診療のみの医療機関ID
export const RE_EXAM_ONLY_HOSPITAL_IDS = [22, 26]; // 本番の医療機関ID

export const DATE_FORMAT = 'YYYY-MM-DD';

export const TIME_FORMAT = 'HH:mm';

export const enum HOSPITAL_KARTE_STATUS {
  NO_USE = 0, // 利用なし
  INITIAL_MASTER_DATA_CREATION_IN_PROGRESS = 1, // 申込中
  INITIAL_MASTER_DATA_CREATION_COMPLETED = 2, // 申込中（初期マスター作成済み）
  DEMO_IN_USE = 3, // デモ利用中
  REAL_IN_USE = 4, // リアル利用中
  DEMO_STOPPED = 5, // デモ利用中止
  REAL_STOPPED = 6, // リアル停止中
}

export const INTERNAL_API_KEY_HEADER = 'X-Internal-API-Key';
