import { Injectable } from '@nestjs/common';

import HasuraService, { HasuraResponse } from '@/common/service/hasura.service';

import {
  BookingFromCalendarOrPortalInput,
  UpdateBookingInfoInput,
  UpdateReceptionStaticCellRequestInput,
} from './raiin.input';

@Injectable()
export default class RaiinService {
  constructor(private readonly hasuraService: HasuraService) {}

  async registerRaiinInfo(
    input: BookingFromCalendarOrPortalInput,
  ): Promise<HasuraResponse> {
    const mutation = `
      mutation ($input: EmrCloudApiRequestsReceptionBookingFromCalendarOrPortalRequestInput!) {
        postApiReceptionBookingFromCalendarOrPortal(emrCloudApiRequestsReceptionBookingFromCalendarOrPortalRequestInput: $input) {
          data {
            raiinNo
          }
          message
          status
        }
      }
    `;

    const variables: { input: BookingFromCalendarOrPortalInput } = {
      input,
    };

    return await this.hasuraService.sendHasuraRequest(mutation, variables);
  }

  async deleteRaiinInfo(
    input: UpdateReceptionStaticCellRequestInput,
  ): Promise<HasuraResponse> {
    const mutation = `
      mutation ($input: EmrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput!) {
        putApiVisitingUpdateStaticCell(emrCloudApiRequestsReceptionUpdateReceptionStaticCellRequestInput: $input) {
          message
          status
          data {
            success
          }
        }
      }
    `;

    const variables: { input: UpdateReceptionStaticCellRequestInput } = {
      input,
    };

    return await this.hasuraService.sendHasuraRequest(mutation, variables);
  }

  async updateRaiinInfo(
    input: UpdateBookingInfoInput,
  ): Promise<HasuraResponse> {
    const mutation = `
      mutation($input: EmrCloudApiRequestsPatientInforUpdateBookingInfoRequestInput!) {
        postApiReceptionUpdateBookingInfo(emrCloudApiRequestsPatientInforUpdateBookingInfoRequestInput: $input) {
          message
          status
          data {
          listRaiinNo
          }
        }
      }
    `;

    const variables: { input: UpdateBookingInfoInput } = { input };

    return await this.hasuraService.sendHasuraRequest(mutation, variables);
  }
}
