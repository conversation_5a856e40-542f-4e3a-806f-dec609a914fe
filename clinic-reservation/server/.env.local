# Endpoints
APP_URL="http://localhost:3123"
API_URL="http://localhost:3100"
DENKARU_API_ENDPOINT="http://localhost:8080"
AUTH_SERVER_URL="http://localhost:8080/auth"
URL_BASE_SERVER_GRAPHQL_HASURA="http://localhost:8090/v1/graphql"

# Database
DATABASE_URL="postgre://root:root@localhost:5433/local_gmoht"
DATABASE_SCHEMA="local_gmoht"

NEST_PORT="3100"
CORS="http://localhost:3123"
ENV="local"
DOMAIN_CLINIC_MAP="click-sec.org"

# JWT
JWT_ACCESS_SECRET="JWT_ACCESS_SECRET"
JWT_REFRESH_SECRET="JWT_REFRESH_SECRET"
LOCAL_ACCESS_KEY_ID="********************"
LOCAL_SECRET_ACCESS_KEY="ypKyUIuycjeu5h3wU/BSxUmW3zllVRcqDn2JKUfJ"
SUPPORT_MAIL_ADDRESS="<EMAIL>"

# Recaptcha
RECAPTCHA_SECRET_KEY="6LfTXTUqAAAAAHrLR1F-NQOKUUtEJ_MDsDcOfibf"
RECAPTCHA_V2_SECRET_KEY="6LctXzUqAAAAAKdLx47OFbpJ8XuMmdwkkrMiGP7C"

# LINE
LINE_MESSAGING_CHANNEL_SECRET="f9bca9484acfa311befdf4b611973a86"
LINE_MESSAGING_CHANNEL_ACCESS_TOKEN="RjwKg7M2uv2fhaSLK2kJuADcru6p8cOFO5fxbbicvgvvArodwd32qOZ/wLOQhwZuuLIKeE81saIXVyZCoWeHYSZ4vQo0N2M6BQT10xhhKB5ImK8e3d1eQhVPHLTDoMbJqwd6ab0OA4XZnLMCUlkp5AdB04t89/1O/w1cDnyilFU="

# Redis
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_IS_CLUSTER_MODE=false

# SENTRY_DSN="https://<EMAIL>/4505628211806208"

# OpenSearch
# make start_es実行が必要
OPEN_SEARCH_URL="http://localhost:9200"
OPEN_SEARCH_PASSWORD=devclinic
OPEN_SEARCH_USER=devclinic
HOSPITAL_INDEX=development-hospital

# Fincode
FINCODE_BASE_URL_JS=https://api.test.fincode.jp
FINCODE_INSURANCE_MEDICAL_SK=m_test_NDhlZDhmZTEtY2ZkZC00ZDk4LWE4OGEtZWNiMTY2ZjZiY2VmYTM5Mjg4ZTEtZWY0Yi00MGZiLTkyZTEtOWE0MTY5MzU3ZDMxc18yNDA2MTc1MzY1Mg
FINCODE_FREE_MEDICAL_SK=m_test_OWZiMTVkMTktOTU4ZC00NjQ4LTlhMjAtMDUzMTY2ZDIzYTJmNmQxMTFhMDAtNzk3Ni00MjA2LWI5YmUtYzE5NGM1MjM5MmM2c18yNDA2MTc0ODA0Ng
FINCODE_HT_INSURANCE_MEDICAL_SK=m_test_NmY1OTBhZjAtYzc5OC00NzIxLWE0MWItOTYwZmRmMjU1Zjg3MTQyMjM5MmItMzU1Zi00N2U3LWEzODItMTRiZjJkOGY5OGZlc18yNTAxMDg2Nzk1OQ
FINCODE_HT_FREE_MEDICAL_SK=m_test_NjYwMWNiM2MtOGE4Yi00Yzc4LWI2NmItZjJjZGZjNWQ3NTQ4YmZhNzQ2NDktMzdhZC00Y2Y4LWExMjgtZTYwZmJhNzQxMjgyc18yNTAxMDg5NTcxNQ

# S3
AWS_S3_CUSTOMER_FILES_BUCKET=tf-booking-dev1-customer-files
AWS_S3_SURVEY_FILE_BUCKET="tf-booking-dev1-survey-files"

# Zendesk
ZENDESK_SSO_JWT_SECRET_KEY="1xGX3idKuZ7BgB3y0pto1MTyy7zvHuaM8WkaPtzA6R43tjrh"
ZENDESK_SSO_ENDPOINT_URL="https://gmohc1721969772.zendesk.com/access/jwt"
ZENDESK_SSO_FIRST_ACCESS_PAGE="https://gmohc1721969772.zendesk.com/hc/ja"
ZENDESK_SSO_RESERVATION_ORGANIZATION_ID="35552579433881"

# Queue
# make start_sqs実行が必要
QUEUE_URL_SMS="http://localhost:9324/queue/sms.fifo"
QUEUE_URL_MAIL="http://localhost:9324/queue/mail"

# Yakkyoku24_HPID
YAKKYOKU24_HPID="7"
EMAIL_ADDRESS_YAKKYOKU24_OFFICE="<EMAIL>"

IS_CLOSE_GRAPHQL_PLAYGROUND=false

# falseにした場合、denkaru-serverで「make start_sqs」「make start_sms_send」を実行すること
IS_SKIP_OTP=true

TREATMENT_CACHE_TTL=60

# internal API KEY
INTERNAL_API_KEY="internalapikey"
