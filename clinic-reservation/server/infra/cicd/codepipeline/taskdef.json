{"family": "<TASK_DEFINITION_FAMILY>", "containerDefinitions": [{"name": "<CONTAINER_NAME>", "image": "<IMAGE1_NAME>", "cpu": 0, "memory": 4000, "portMappings": [{"containerPort": 3000, "hostPort": 3000, "protocol": "tcp"}], "essential": true, "entryPoint": ["yarn", "start:api:prod"], "environment": [{"name": "TZ", "value": "Asia/Tokyo"}], "mountPoints": [], "volumesFrom": [], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/DATABASE_URL"}, {"name": "FINCODE_BASE_URL_JS", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/FINCODE_BASE_URL_JS"}, {"name": "FINCODE_INSURANCE_MEDICAL_SK", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/FINCODE_INSURANCE_MEDICAL_SK"}, {"name": "FINCODE_FREE_MEDICAL_SK", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/FINCODE_FREE_MEDICAL_SK"}, {"name": "FINCODE_HT_INSURANCE_MEDICAL_SK", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/FINCODE_HT_INSURANCE_MEDICAL_SK"}, {"name": "FINCODE_HT_FREE_MEDICAL_SK", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/FINCODE_HT_FREE_MEDICAL_SK"}, {"name": "JWT_ACCESS_SECRET", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/JWT_ACCESS_SECRET"}, {"name": "JWT_REFRESH_SECRET", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/JWT_REFRESH_SECRET"}, {"name": "LINE_MESSAGING_CHANNEL_ACCESS_TOKEN", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/LINE_MESSAGING_CHANNEL_ACCESS_TOKEN"}, {"name": "LINE_MESSAGING_CHANNEL_SECRET", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/LINE_MESSAGING_CHANNEL_SECRET"}, {"name": "OPEN_SEARCH_PASSWORD", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/OPEN_SEARCH_PASSWORD"}, {"name": "OPEN_SEARCH_URL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/OPEN_SEARCH_URL"}, {"name": "OPEN_SEARCH_USER", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/OPEN_SEARCH_USER"}, {"name": "RECAPTCHA_SECRET_KEY", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/RECAPTCHA_SECRET_KEY"}, {"name": "RECAPTCHA_V2_SECRET_KEY", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/RECAPTCHA_V2_SECRET_KEY"}, {"name": "SENTRY_DSN", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/BOOKING/<ENV_PARAMETER>/SENTRY_DSN"}, {"name": "API_URL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/API_URL"}, {"name": "APP_URL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/APP_URL"}, {"name": "AUTH_SERVER_URL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/AUTH_SERVER_URL"}, {"name": "AWS_S3_CUSTOMER_FILES_BUCKET", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/AWS_S3_CUSTOMER_FILES_BUCKET"}, {"name": "AWS_S3_SURVEY_FILE_BUCKET", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/AWS_S3_SURVEY_FILE_BUCKET"}, {"name": "CORS", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/CORS"}, {"name": "DATABASE_SCHEMA", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/DATABASE_SCHEMA"}, {"name": "DENKARU_API_ENDPOINT", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/DENKARU_API_ENDPOINT"}, {"name": "DOMAIN_CLINIC_MAP", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/DOMAIN_CLINIC_MAP"}, {"name": "ENV", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/ENV"}, {"name": "IS_CLOSE_GRAPHQL_PLAYGROUND", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/IS_CLOSE_GRAPHQL_PLAYGROUND"}, {"name": "IS_SKIP_OTP", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/IS_SKIP_OTP"}, {"name": "HOSPITAL_INDEX", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/OS_HOSPITAL_INDEX"}, {"name": "QUEUE_URL_SMS", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/QUEUE_URL_SMS"}, {"name": "QUEUE_URL_MAIL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/QUEUE_URL_MAIL"}, {"name": "REDIS_HOST", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/REDIS_HOST"}, {"name": "REDIS_IS_CLUSTER_MODE", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/REDIS_IS_CLUSTER_MODE"}, {"name": "REDIS_PORT", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/REDIS_PORT"}, {"name": "SUPPORT_MAIL_ADDRESS", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/SUPPORT_MAIL_ADDRESS"}, {"name": "YAKKYOKU24_HPID", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/DENKARU/<ENV_PARAMETER>/SERVER/YAKKYOKU24_HPID"}, {"name": "EMAIL_ADDRESS_YAKKYOKU24_OFFICE", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/DENKARU/<ENV_PARAMETER>/SERVER/EMAIL_ADDRESS_YAKKYOKU24_OFFICE"}, {"name": "ZENDESK_SSO_ENDPOINT_URL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/ZENDESK_SSO_ENDPOINT_URL"}, {"name": "ZENDESK_SSO_FIRST_ACCESS_PAGE", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/ZENDESK_SSO_FIRST_ACCESS_PAGE"}, {"name": "ZENDESK_SSO_JWT_SECRET_KEY", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/ZENDESK_SSO_JWT_SECRET_KEY"}, {"name": "ZENDESK_SSO_RESERVATION_ORGANIZATION_ID", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/ZENDESK_SSO_RESERVATION_ORGANIZATION_ID"}, {"name": "URL_BASE_SERVER_GRAPHQL_HASURA", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/DENKARU/<ENV_PARAMETER>/SERVER/URL_BASE_SERVER_GRAPHQL_HASURA"}, {"name": "TREATMENT_CACHE_TTL", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/BOOKING/<ENV_PARAMETER>/TREATMENT_CACHE_TTL"}, {"name": "INTERNAL_API_KEY", "valueFrom": "arn:aws:ssm:ap-northeast-1:<AWS_ACCOUNT_ID>:parameter/TF/DENKARU/<ENV_PARAMETER>/SERVER/INTERNAL_API_KEY"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-create-group": "true", "awslogs-group": "/ecs/tf-booking-<ENV>-api", "awslogs-region": "ap-northeast-1", "awslogs-stream-prefix": "ecs"}}, "systemControls": []}], "taskRoleArn": "arn:aws:iam::<AWS_ACCOUNT_ID>:role/service-role/<TASKROLE>", "executionRoleArn": "arn:aws:iam::<AWS_ACCOUNT_ID>:role/service-role/<EXECUTIONROLE>", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "runtimePlatform": {"operatingSystemFamily": "LINUX"}}