import { useCallback, useState, useMemo } from "react";

import { ApolloError } from "@apollo/client";

import { useErrorHandler } from "@/hooks/useErrorHandler";
import { logger } from "@/utils/sentry-logger";
import { usePostApiReceiptGetListMutation } from "@/apis/gql/operations/__generated__/receipt";

import { convertFormInputToSearchInput } from "../utils/search-criteria";

import type { Receipt } from "../types/list";
import type { SearchCriteriaFormType } from "../types/search-criteria";

export type PaginationState = {
  currentPage: Receipt[];
  allData: Receipt[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasNextPage: boolean;
  error: string | null;
};

export type SortConfig = {
  sortKey: number; // 0: 患者番号, 1: 氏名, 2: レセプト種別, 3: 最終来院日
  sortOrder: boolean; // true: 昇順, false: 降順
};

export type FilterConfig = {
  filterType: number; // 0: すべて, 1: 社保, 2: 国保, 3: その他
};

export type PaginationConfig = {
  limit: number;
  sort: SortConfig;
  filter: FilterConfig;
};

const DEFAULT_PAGINATION_CONFIG: PaginationConfig = {
  limit: 11,
  sort: {
    sortKey: 0, // 患者番号
    sortOrder: true, // 昇順
  },
  filter: {
    filterType: 0, // すべて
  },
};

export const useReceiptPagination = (
  baseSearchCriteria: SearchCriteriaFormType,
) => {
  const { handleError } = useErrorHandler();
  const [getReceiptList] = usePostApiReceiptGetListMutation();

  const [state, setState] = useState<PaginationState>({
    currentPage: [],
    allData: [],
    isLoading: false,
    isLoadingMore: false,
    hasNextPage: true,
    error: null,
  });

  const [config, setConfig] = useState<PaginationConfig>(
    DEFAULT_PAGINATION_CONFIG,
  );

  // Store current search criteria to use in API calls
  const [currentSearchCriteria, setCurrentSearchCriteria] =
    useState<SearchCriteriaFormType>(baseSearchCriteria);

  // Get cursor values from last record for next page
  const getLastRecordCursor = useCallback(() => {
    const lastRecord = state.allData[state.allData.length - 1];
    if (!lastRecord) {
      return {};
    }

    return {
      cursorPtId: lastRecord.ptId,
      cursorSinYm: lastRecord.sinYm,
      cursorHokenId: lastRecord.hokenId,
    };
  }, [state.allData]);

  // Create search input with pagination parameters
  const createSearchInput = useCallback(
    (isFirstPage: boolean = false) => {
      const paginationParams = {
        limit: config.limit,
        filterType: config.filter.filterType,
        sortKey: config.sort.sortKey,
        sortOrder: config.sort.sortOrder,
        ...(isFirstPage ? {} : getLastRecordCursor()),
      };

      return convertFormInputToSearchInput({
        ...currentSearchCriteria,
        ...paginationParams,
      });
    },
    [currentSearchCriteria, config, getLastRecordCursor],
  );

  // Load first page
  const loadFirstPage = useCallback(async () => {
    setState((prev) => ({
      ...prev,
      isLoading: true,
      error: null,
      allData: [],
      currentPage: [],
      hasNextPage: true,
    }));

    try {
      const queryInput = createSearchInput(true);
      const response = await getReceiptList({
        variables: { input: queryInput },
      });

      const receiptList =
        response.data?.postApiReceiptGetList?.data?.receiptList ?? [];

      setState((prev) => ({
        ...prev,
        isLoading: false,
        currentPage: receiptList,
        allData: receiptList,
        hasNextPage: receiptList.length === config.limit,
      }));
    } catch (error) {
      logger({ error, message: "failed to fetch receipt list first page" });
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "レセプト一覧の取得に失敗しました",
      }));

      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "レセプト一覧の取得に失敗しました",
        });
      }
    }
  }, [createSearchInput, getReceiptList, config.limit, handleError]);

  // Load more data for infinite scroll
  const loadMore = useCallback(async () => {
    if (state.isLoadingMore || !state.hasNextPage) {
      return;
    }

    setState((prev) => ({
      ...prev,
      isLoadingMore: true,
      error: null,
    }));

    try {
      const queryInput = createSearchInput(false);
      const response = await getReceiptList({
        variables: { input: queryInput },
      });

      const newReceiptList =
        response.data?.postApiReceiptGetList?.data?.receiptList ?? [];

      setState((prev) => ({
        ...prev,
        isLoadingMore: false,
        currentPage: newReceiptList,
        allData: [...prev.allData, ...newReceiptList],
        hasNextPage: newReceiptList.length === config.limit,
      }));
    } catch (error) {
      logger({ error, message: "failed to fetch more receipt list" });
      setState((prev) => ({
        ...prev,
        isLoadingMore: false,
        error: "追加データの取得に失敗しました",
      }));

      if (error instanceof ApolloError || error instanceof Error) {
        handleError({
          error,
          commonMessage: "追加データの取得に失敗しました",
        });
      }
    }
  }, [
    createSearchInput,
    getReceiptList,
    config.limit,
    state.isLoadingMore,
    state.hasNextPage,
    handleError,
  ]);

  // Update sort configuration
  const updateSort = useCallback((sortKey: number, sortOrder: boolean) => {
    setConfig((prev) => ({
      ...prev,
      sort: { sortKey, sortOrder },
    }));
  }, []);

  // Update filter configuration
  const updateFilter = useCallback((filterType: number) => {
    setConfig((prev) => ({
      ...prev,
      filter: { filterType },
    }));
  }, []);

  // Update search criteria and reset pagination
  const updateSearchCriteria = useCallback(
    (newCriteria: SearchCriteriaFormType) => {
      setCurrentSearchCriteria(newCriteria);
      setState({
        currentPage: [],
        allData: [],
        isLoading: false,
        isLoadingMore: false,
        hasNextPage: true,
        error: null,
      });
    },
    [],
  );

  // Reset pagination when search criteria or config changes
  const reset = useCallback(() => {
    setState({
      currentPage: [],
      allData: [],
      isLoading: false,
      isLoadingMore: false,
      hasNextPage: true,
      error: null,
    });
  }, []);

  // Remove automatic reset on criteria change to avoid infinite loops
  // Reset will be called manually when needed (e.g., when search is triggered)

  // Memoized return value
  const returnValue = useMemo(
    () => ({
      // State
      ...state,
      config,

      // Actions
      loadFirstPage,
      loadMore,
      updateSort,
      updateFilter,
      updateSearchCriteria,
      reset,

      // Computed values
      totalCount: state.allData.length,
      isEmpty: state.allData.length === 0 && !state.isLoading,
    }),
    [
      state,
      config,
      loadFirstPage,
      loadMore,
      updateSort,
      updateFilter,
      updateSearchCriteria,
      reset,
    ],
  );

  return returnValue;
};

export type UseReceiptPaginationReturn = ReturnType<
  typeof useReceiptPagination
>;
