import { createContext, useContext, useMemo } from "react";

import type { ReactNode } from "react";
import type { UseReceiptPaginationReturn } from "./useReceiptPagination";

type ContextType = UseReceiptPaginationReturn | null;

const PaginationContext = createContext<ContextType>(null);

export const useReceiptPaginationContext = () => {
  const context = useContext(PaginationContext);

  if (!context) {
    throw new Error(
      "useReceiptPaginationContextがPaginationProviderでラップされていません",
    );
  }

  return context;
};

export const PaginationProvider: React.FC<{
  children: ReactNode;
  pagination: UseReceiptPaginationReturn;
}> = ({ children, pagination }) => {
  const value = useMemo(() => pagination, [pagination]);

  return (
    <PaginationContext.Provider value={value}>
      {children}
    </PaginationContext.Provider>
  );
};
