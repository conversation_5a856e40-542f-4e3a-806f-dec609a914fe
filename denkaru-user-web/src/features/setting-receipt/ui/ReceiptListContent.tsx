import { useState } from "react";

import Link from "next/link";
import { useRouter } from "next/router";
import qs from "qs";
import { Controller } from "react-hook-form";
import styled, { css } from "styled-components";

import { DatePicker } from "@/components/ui/DatePicker";
import { DropdownMenu } from "@/components/ui/DropdownMenu";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { SvgIconMore } from "@/components/ui/Icon/IconMore";
import { SvgIconTableSorterDown } from "@/components/ui/Icon/IconTableSorterDown";
import { SvgIconTableSorterUp } from "@/components/ui/Icon/IconTableSorterUp";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { But<PERSON> } from "@/components/ui/NewButton";
import { Table } from "@/components/ui/Table";
import { Tabs } from "@/components/ui/Tabs";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";

import { kokuHoDict, shaHoDict } from "../constants/list";
import { SelectRecordProvider } from "../hooks/pending/usePendingSelectProviders";
import { useModal } from "../hooks/useReceiptListModalProviders";
import { useReceiptSearch } from "../hooks/useReceiptListSearchProvider";
import { useReceiptPaginationContext } from "../hooks/useReceiptPaginationProvider";
import { useSelectRecord } from "../hooks/useReceiptSelectProviders";
import { convert6digitToYYYYMMwithSlash } from "../utils";
import { getKohiCountName } from "../utils/list";

import { CancelClaimModal } from "./cancel-claim/CancelClaimModal";
import { DataCreationModal } from "./data-creation/DataCreationModal";
import { ExportCsvListModal } from "./export-csv/ExportCsvModal";
import { PendingListModal } from "./pending/PendingListModal";
import { PrintModal } from "./print/PrintModal";
import { PrintListModal } from "./print-list/PrintListModal";
import { RecalculationModal } from "./recalculation/RecalculationModal";
import { SearchCriteriaModal } from "./search-criteria/SearchCriteriaModal";

import type { FilterTabs, Receipt } from "../types/list";
import type { TableColumnsType } from "antd";
import type { FC } from "react";

const ListHeader = styled.div`
  padding: 20px 20px 0 20px;
  background-color: #fff;
  border-bottom: 1px solid #e2e3e5;
`;

const HeaderTopWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

const Title = styled.p`
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const ActionWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

const ActionButtonsWrapper = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled(Button)``;

const DatePickerWrapper = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
`;

const StyledTable = styled(Table)`
  padding: 20px;

  table {
    border-collapse: collapse;
  }

  .ant-table-row {
    cursor: pointer;

    .ant-table-cell {
      border: 1px solid #e0e6ec;
    }
  }

  .ant-table-tbody > tr:not(.ant-table-measure-row) > td {
    padding: 8px !important;
  }
`;

const StyledButton = styled(Button)`
  width: 80px;
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav-wrap {
    padding-left: 0;

    &::before {
      display: none;
    }
  }

  .ant-tabs-nav {
    margin: 0;

    &::before {
      border-bottom: 0;
    }
  }

  .ant-tabs-tab {
    width: 100px !important;
  }
`;

const HeaderBottomWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ButtonInTab = styled(Button)``;

const ListInfoWrapper = styled.div`
  display: flex;
  gap: 20px;
  color: #6a757d;
`;

const ConfirmBadge = styled.span<{ $isDone?: boolean }>`
  display: inline-block;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  white-space: nowrap;
  background-color: ${({ $isDone }) => {
    if ($isDone) {
      return "#32c86e";
    }

    return "#ff7373";
  }};
  padding: 4px;
  border-radius: 2px;
`;

const PatientInfo = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
  gap: 4px;
`;

const Name = styled.p`
  font-size: 14px;
  color: #007aff;
`;

const SubName = styled.p`
  font-size: 12px;
`;

const GenderBagde = styled.div<{ $gender: number }>`
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  border-radius: 2px;
  padding: 3px 6px;
  line-height: 1;

  ${({ $gender }) => {
    if ($gender === 1) {
      return css`
        background-color: #006ec3;
      `;
    }

    if ($gender === 2) {
      return css`
        background-color: #f48e91;
      `;
    }

    return undefined;
  }}
`;

const CancelText = styled.p`
  color: #e74c3c;
`;

const TestPatientBadge = styled.div`
  width: 68px;
  height: 16px;
  margin: 0 auto;
  border-radius: 2px;
  background-color: #a2aeb8;
  font-size: 11px;
  color: #fff;
`;

const PreviewMenu = ({ receipt }: { receipt: Receipt }) => {
  const { ptId, hokenId, sinYm } = receipt;
  const SCREEN_TYPE = 3;
  const { getValues } = useReceiptSearch();

  const { seikyuYm } = getValues();
  const queryParams = qs.stringify({
    ptId: ptId ?? 0,
    seikyuYM: seikyuYm?.format("YYYYMM") ?? "0",
    hokenId: hokenId ?? 0,
    sinYM: sinYm ?? 0,
    receiptPreviewScreen: SCREEN_TYPE,
  });

  return (
    <Link href={`/setting/receipt/preview?${queryParams}`} target="_blank">
      プレビュー
    </Link>
  );
};

type Props = {
  receiptList: Receipt[];
  isLoading: boolean;
};

export const ReceiptListContent: FC<Props> = ({ receiptList, isLoading }) => {
  const { modal, handleOpenModal } = useModal();
  const { handleSelectRecord, handleUnSelectRecord } = useSelectRecord();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<FilterTabs>("ALL");
  const { control, getValues, search } = useReceiptSearch();
  const pagination = useReceiptPaginationContext();

  // Setup infinite scroll
  const { infiniteRef } = useInfiniteScroll(() => {
    if (pagination.hasNextPage && !pagination.isLoadingMore) {
      pagination.loadMore();
    }
  });

  const selectRecord = (record: Receipt) => {
    handleUnSelectRecord(); // 選択を初期化
    handleSelectRecord(record);
  };

  const onClickRow = (receipt: Receipt) => {
    const { seikyuYm } = getValues();
    const { ptId, ptNum, sinYm, hokenId, hokenKbn, seikyuKbn } = receipt;

    const query = {
      seikyuYm: seikyuYm?.format("YYYYMM").toString() ?? "",
      ptId: ptId ?? "0",
      ptNum: ptNum ?? "0",
      sinDate: (sinYm ?? 0) * 100 + 1,
      sinYm: sinYm ?? 0,
      hokenId: hokenId ?? 0,
      hokenKbn: hokenKbn ?? 0,
      seikyuKbn: seikyuKbn ?? 0,
    };

    router.push({
      pathname: "/setting/receipt/check",
      query,
    });
  };

  /**
   * getColumnDefsを参考に実装
   * @see: https://github.com/bizleap-healthcare/smartkarte-user-web/blob/7125471a8108c1ecd0cf74c9180d30c4d23c6667/src/pages/rece/Rece.helper.tsx#L17-L552 */
  const columns: TableColumnsType<Receipt> = [
    {
      title: "請求",
      align: "center",
      width: 70,
      render: (_, { seikyuKbn }) => {
        if (seikyuKbn === 1) {
          return <p>月遅れ</p>;
        }
        if (seikyuKbn === 2) {
          return <p>返戻</p>;
        }
        if (seikyuKbn === 3) {
          return <p>オ返戻</p>;
        }
        return "";
      },
    },
    {
      title: "診療年月",
      align: "center",
      width: 100,
      render: (_, { sinYm }) => {
        if (!sinYm || String(sinYm).length !== 6) {
          return String(sinYm);
        }
        return convert6digitToYYYYMMwithSlash(sinYm);
      },
    },
    {
      title: "紙",
      align: "center",
      width: 50,
      render: (_, { isPaperRece, hokenKbn, seikyuKbn }) => {
        if (
          isPaperRece === 1 ||
          seikyuKbn === 2 ||
          hokenKbn === 0 ||
          hokenKbn === 14
          // TODO: rousaiRecedenLicense/aftercareDensanLicenseはAIチャートでは考慮不要？
          // ((rousaiRecedenLicense !== 1 || (seikyuYm && rousaiRecedenLicense === 1 && seikyuYm < Number(rousaiRecedenStartYm))) && (hokenKbn === 11 || hokenKbn === 12)) ||
          // ((aftercareDensanLicense !== 1 || (seikyuYm && aftercareDensanLicense === 1 && seikyuYm < Number(aftercareDensanTerm))) && hokenKbn === 13)
        ) {
          return "○";
        }
        return "";
      },
    },
    {
      title: "確認",
      align: "center",
      width: 80,
      render: (_, { statusKbn }) => {
        if (statusKbn === 0) {
          return <ConfirmBadge>未</ConfirmBadge>;
        }
        if (statusKbn === 1) {
          return <ConfirmBadge>シス保留</ConfirmBadge>;
        }
        if (statusKbn === 2) {
          return <ConfirmBadge>保留</ConfirmBadge>;
        }
        if (statusKbn === 3) {
          return <ConfirmBadge>保留2</ConfirmBadge>;
        }
        if (statusKbn === 4) {
          return <ConfirmBadge>保留3</ConfirmBadge>;
        }
        if (statusKbn === 8) {
          return <ConfirmBadge>仮</ConfirmBadge>;
        }
        if (statusKbn === 9) {
          return <ConfirmBadge $isDone>済</ConfirmBadge>;
        }

        return "";
      },
    },
    {
      title: "患者番号",
      align: "center",
      width: 100,
      sorter: {
        compare: (a, b) => {
          return Number(a.ptNum ?? 0) - Number(b.ptNum ?? 0);
        },
      },
      showSorterTooltip: false,
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      render: (_, { ptNum, isPtTest }) => (
        <p>
          {isPtTest && <TestPatientBadge>テスト患者</TestPatientBadge>}
          {ptNum ?? ""}
        </p>
      ),
    },
    {
      title: "氏名",
      width: 200,
      sorter: (a, b) => (a.kanaName || "").localeCompare(b.kanaName || ""),
      showSorterTooltip: false,
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      render: (_, { kanaName, name, sex, age }) => {
        return (
          <div>
            <SubName>{kanaName}</SubName>
            <PatientInfo>
              <Name>{name}</Name>
              <GenderBagde $gender={sex ?? 0}>
                <p>{sex === 1 ? "M" : sex === 2 ? "F" : "　"}</p>
              </GenderBagde>
              <p style={{ flexShrink: 0 }}>{age}</p>
            </PatientInfo>
          </div>
        );
      },
    },
    {
      title: "レセプト種別",
      width: 170,
      sorter: {
        compare: (a, b) => {
          return Number(a.hokenKbn ?? 0) - Number(b.hokenKbn ?? 0);
        },
      },
      showSorterTooltip: false,
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      render: (_, { hokenKbn, receSbt }) => {
        if (hokenKbn === 0) {
          if (receSbt?.length === 4) {
            let result = "";
            if (receSbt[0] === "8") {
              result = "自費";
            } else if (receSbt[0] === "9") {
              result = "自費レセ";
            }
            const kohiCount = Number(receSbt[2]);
            if (kohiCount > 0) {
              const prefix = getKohiCountName(kohiCount);
              return result + prefix;
            }
          }
          return "";
        }
        if (hokenKbn === 1) {
          return typeof receSbt === "string" && receSbt in shaHoDict
            ? shaHoDict[receSbt]
            : "";
        }
        if (hokenKbn === 2) {
          return typeof receSbt === "string" && receSbt in kokuHoDict
            ? kokuHoDict[receSbt]
            : "";
        }
        if (hokenKbn === 11) {
          return "労災(短期給付)";
        }
        if (hokenKbn === 12) {
          return "労災(傷病年金)";
        }
        if (hokenKbn === 13) {
          return "アフターケア";
        }
        if (hokenKbn === 14) {
          return "自賠責";
        }
        return "";
      },
    },
    {
      title: "診療情報",
      width: 170,
      render: (_, { kaName, sName }) => (
        <p>
          {kaName ? `${kaName}：` : ""}
          {sName ?? ""}
        </p>
      ),
    },
    {
      title: "最終来院日",
      align: "center",
      width: 110,
      sorter: {
        compare: (a, b) => {
          return Number(a.lastVisitDate ?? 0) - Number(b.lastVisitDate);
        },
      },
      showSorterTooltip: false,
      sortIcon: ({ sortOrder }) =>
        sortOrder === "ascend" ? (
          <SvgIconTableSorterUp />
        ) : sortOrder === "descend" ? (
          <SvgIconTableSorterDown />
        ) : null,
      render: (_, { lastVisitDateDisplay }) => <p>{lastVisitDateDisplay}</p>,
    },
    {
      title: "保険者番号",
      align: "center",
      width: 100,
      render: (_, { hokensyaNo }) => <p>{hokensyaNo}</p>,
    },
    {
      title: "点数",
      width: 80,
      render: (_, { tensu }) => <p style={{ textAlign: "right" }}>{tensu}</p>,
    },
    {
      title: "実日数",
      align: "center",
      width: 70,
      render: (_, { hokenSbtCd, kohi1Nissu, hokenNissu }) => {
        if (String(hokenSbtCd).startsWith("5")) {
          return kohi1Nissu ?? "";
        }
        return hokenNissu ?? "";
      },
    },
    {
      title: "コメント",
      align: "center",
      width: 100,
      render: (_, { isReceCmtExist }) => {
        return isReceCmtExist === 1 ? "◯" : "";
      },
    },
    {
      title: "症状詳記",
      align: "center",
      width: 100,
      render: (_, { isSyoukiInfExist }) => {
        return isSyoukiInfExist === 1 ? "◯" : "";
      },
    },
    {
      title: "傷病経過",
      align: "center",
      width: 100,
      render: (_, { isSyobyoKeikaExist }) => {
        return isSyobyoKeikaExist === 1 ? "◯" : "";
      },
    },
    {
      title: "再請求コメント",
      align: "center",
      width: 160,
      render: (_, { receSeikyuCmt }) => {
        return receSeikyuCmt ?? "";
      },
    },
    {
      title: "",
      width: 40,
      render: (_, record) => {
        return (
          <DropdownMenu
            trigger={["click"]}
            menu={{
              items: [
                {
                  key: "PREVIEW",
                  label: <PreviewMenu receipt={record} />,
                },
                {
                  key: "CANCEL",
                  label: <CancelText>請求取り消し</CancelText>,
                  onClick: () => {
                    selectRecord(record);
                    handleOpenModal("CANCEL_CLAIM");
                  },
                },
              ],
            }}
          >
            <IconButton varient="icon-only" icon={<SvgIconMore />} />
          </DropdownMenu>
        );
      },
      onCell: () => {
        return {
          onClick: (e) => {
            e.stopPropagation();
          },
        };
      },
    },
  ];

  // Handle tab change - update filter and reload data
  const handleTabChange = async (key: string) => {
    if (
      key === "ALL" ||
      key === "SHAHO" ||
      key === "KOKUHO" ||
      key === "OTHERS"
    ) {
      setActiveTab(key);

      // Map tab to filterType
      let filterType = 0; // ALL
      if (key === "SHAHO") filterType = 1;
      else if (key === "KOKUHO") filterType = 2;
      else if (key === "OTHERS") filterType = 3;

      // Update filter and reload data
      pagination.updateFilter(filterType);
      pagination.reset();
      await pagination.loadFirstPage();
    }
  };

  // Use receiptList directly since filtering is now server-side
  const filteredList = receiptList;

  const totalPoint = filteredList.reduce(
    (total, item) => total + (item.tensu || 0),
    0,
  );

  return (
    <>
      <ListHeader>
        <HeaderTopWrapper>
          <Title>レセプト</Title>
          <DropdownMenu
            trigger={["click"]}
            menu={{
              items: [
                {
                  key: "PRINT",
                  label: "レセプトチェック一覧印刷",
                  onClick: () => handleOpenModal("PRINT_LIST"),
                },
                {
                  key: "CSV",
                  label: "CSV保存",
                  onClick: () => handleOpenModal("EXPORT_CSV"),
                },
              ],
            }}
          >
            <IconButton varient="square" icon={<SvgIconMore />} />
          </DropdownMenu>
        </HeaderTopWrapper>

        <StyledLabel label="処理年月" />
        <ActionWrapper>
          <DatePickerWrapper>
            <Controller
              name="seikyuYm"
              control={control}
              render={({ field }) => (
                <DatePicker
                  {...field}
                  picker="month"
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                  format="YYYY/MM"
                  onChange={(e) => {
                    field.onChange(e);
                    search(getValues());
                  }}
                />
              )}
            />

            <StyledButton
              varient="standard-sr"
              onClick={() => handleOpenModal("SEARCH_CRITERIA")}
            >
              条件設定
            </StyledButton>
          </DatePickerWrapper>
          <ActionButtonsWrapper>
            <ActionButton
              varient="secondary"
              onClick={() => handleOpenModal("RECALCULATION")}
            >
              集計(再計算)
            </ActionButton>
            <ActionButton
              varient="secondary"
              onClick={() => handleOpenModal("CREATE_DATA")}
            >
              データ作成
            </ActionButton>
            <ActionButton
              varient="standard"
              onClick={() => handleOpenModal("PRINT")}
            >
              印刷
            </ActionButton>
          </ActionButtonsWrapper>
        </ActionWrapper>
        <HeaderBottomWrapper>
          <StyledTabs
            activeKey={activeTab}
            items={[
              {
                key: "ALL",
                label: "すべて",
              },
              {
                key: "SHAHO",
                label: "社保",
              },
              {
                key: "KOKUHO",
                label: "国保",
              },
              {
                key: "OTHERS",
                label: "その他",
              },
            ]}
            onTabClick={handleTabChange}
            tabBarExtraContent={
              <ButtonInTab
                varient="standard-sr"
                onClick={() => handleOpenModal("PENDING_LIST")}
              >
                請求保留一覧
              </ButtonInTab>
            }
          />
          <ListInfoWrapper>
            <p>{filteredList.length}件</p>
            <p>表示点数{totalPoint.toLocaleString()}点</p>
          </ListInfoWrapper>
        </HeaderBottomWrapper>
      </ListHeader>

      <StyledTable
        sticky
        dataSource={filteredList}
        columns={columns}
        scroll={{ x: "max-content", y: window.innerHeight - 310 }}
        onRow={(record) => {
          return {
            onClick: () => onClickRow(record),
          };
        }}
        loading={isLoading}
      />

      {pagination.hasNextPage && (
        <div ref={infiniteRef} style={{ height: "20px", margin: "10px 0" }}>
          {pagination.isLoadingMore && (
            <div style={{ textAlign: "center" }}>読み込み中...</div>
          )}
        </div>
      )}

      <SearchCriteriaModal />
      {modal.recalculationOpen && <RecalculationModal />}
      {modal.createDataOpen && <DataCreationModal />}
      {modal.printOpen && <PrintModal />}
      {modal.pendingListOpen && (
        <SelectRecordProvider>
          <PendingListModal />
        </SelectRecordProvider>
      )}
      {modal.printListOpen && <PrintListModal receiptList={filteredList} />}
      {modal.exportCsvOpen && <ExportCsvListModal />}
      {modal.cancelClaimOpen && <CancelClaimModal />}
    </>
  );
};
