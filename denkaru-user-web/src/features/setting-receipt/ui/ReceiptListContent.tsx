import { useState, useRef, useEffect } from "react";

import Link from "next/link";
import { useRouter } from "next/router";
import qs from "qs";
import { Controller } from "react-hook-form";
import styled, { css } from "styled-components";

import { DatePicker } from "@/components/ui/DatePicker";
import { DropdownMenu } from "@/components/ui/DropdownMenu";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { SvgIconMore } from "@/components/ui/Icon/IconMore";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import { Tabs } from "@/components/ui/Tabs";
import {
  InfiniteScrollTable,
  TableDataCell,
} from "@/components/common/InfiniteScrollTable";
import { SortOrder } from "@/apis/gql/generated/types";

import { kokuHoDict, shaHoDict } from "../constants/list";
import { SelectRecordProvider } from "../hooks/pending/usePendingSelectProviders";
import { useModal } from "../hooks/useReceiptListModalProviders";
import { useReceiptSearch } from "../hooks/useReceiptListSearchProvider";
import { useReceiptPaginationContext } from "../hooks/useReceiptPaginationProvider";
import { useSelectRecord } from "../hooks/useReceiptSelectProviders";
import { convert6digitToYYYYMMwithSlash } from "../utils";
import { getKohiCountName } from "../utils/list";

import { CancelClaimModal } from "./cancel-claim/CancelClaimModal";
import { DataCreationModal } from "./data-creation/DataCreationModal";
import { ExportCsvListModal } from "./export-csv/ExportCsvModal";
import { PendingListModal } from "./pending/PendingListModal";
import { PrintModal } from "./print/PrintModal";
import { PrintListModal } from "./print-list/PrintListModal";
import { RecalculationModal } from "./recalculation/RecalculationModal";
import { SearchCriteriaModal } from "./search-criteria/SearchCriteriaModal";

import type { FilterTabs, Receipt } from "../types/list";
import type { TableColumn } from "@/components/common/InfiniteScrollTable";
import type { FC } from "react";

const ListHeader = styled.div`
  padding: 20px 20px 0 20px;
  background-color: #fff;
  border-bottom: 1px solid #e2e3e5;
`;

const HeaderTopWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

const Title = styled.p`
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const ActionWrapper = styled.div`
  display: flex;
  justify-content: space-between;
`;

const ActionButtonsWrapper = styled.div`
  display: flex;
  gap: 12px;
`;

const ActionButton = styled(Button)``;

const DatePickerWrapper = styled.div`
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
`;

const TableContainer = styled.div`
  padding: 20px;
  height: calc(100vh - 310px);
`;

const StyledButton = styled(Button)`
  width: 80px;
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav-wrap {
    padding-left: 0;

    &::before {
      display: none;
    }
  }

  .ant-tabs-nav {
    margin: 0;

    &::before {
      border-bottom: 0;
    }
  }

  .ant-tabs-tab {
    width: 100px !important;
  }
`;

const HeaderBottomWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ButtonInTab = styled(Button)``;

const ListInfoWrapper = styled.div`
  display: flex;
  gap: 20px;
  color: #6a757d;
`;

const ConfirmBadge = styled.span<{ $isDone?: boolean }>`
  display: inline-block;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  white-space: nowrap;
  background-color: ${({ $isDone }) => {
    if ($isDone) {
      return "#32c86e";
    }

    return "#ff7373";
  }};
  padding: 4px;
  border-radius: 2px;
`;

const PatientInfo = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
  gap: 4px;
`;

const Name = styled.p`
  font-size: 14px;
  color: #007aff;
`;

const SubName = styled.p`
  font-size: 12px;
`;

const GenderBagde = styled.div<{ $gender: number }>`
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  border-radius: 2px;
  padding: 3px 6px;
  line-height: 1;

  ${({ $gender }) => {
    if ($gender === 1) {
      return css`
        background-color: #006ec3;
      `;
    }

    if ($gender === 2) {
      return css`
        background-color: #f48e91;
      `;
    }

    return undefined;
  }}
`;

const CancelText = styled.p`
  color: #e74c3c;
`;

const TestPatientBadge = styled.div`
  width: 68px;
  height: 16px;
  margin: 0 auto;
  border-radius: 2px;
  background-color: #a2aeb8;
  font-size: 11px;
  color: #fff;
`;

const PreviewMenu = ({ receipt }: { receipt: Receipt }) => {
  const { ptId, hokenId, sinYm } = receipt;
  const SCREEN_TYPE = 3;
  const { getValues } = useReceiptSearch();

  const { seikyuYm } = getValues();
  const queryParams = qs.stringify({
    ptId: ptId ?? 0,
    seikyuYM: seikyuYm?.format("YYYYMM") ?? "0",
    hokenId: hokenId ?? 0,
    sinYM: sinYm ?? 0,
    receiptPreviewScreen: SCREEN_TYPE,
  });

  return (
    <Link href={`/setting/receipt/preview?${queryParams}`} target="_blank">
      プレビュー
    </Link>
  );
};

type Props = {
  receiptList: Receipt[];
};

export const ReceiptListContent: FC<Props> = ({ receiptList }) => {
  const { modal, handleOpenModal } = useModal();
  const { handleSelectRecord, handleUnSelectRecord } = useSelectRecord();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<FilterTabs>("ALL");
  const { control, getValues, search } = useReceiptSearch();
  const pagination = useReceiptPaginationContext();

  // Track if we should allow load more - use a simpler approach
  const allowLoadMore = useRef(false);
  const loadMoreTimer = useRef<NodeJS.Timeout | null>(null);

  // Enable load more after initial data is loaded
  useEffect(() => {
    console.log("useEffect receiptList.length changed:", receiptList.length);
    if (receiptList.length > 0) {
      // Clear any existing timer
      if (loadMoreTimer.current) {
        clearTimeout(loadMoreTimer.current);
      }

      // Set a timer to enable load more after a short delay
      loadMoreTimer.current = setTimeout(() => {
        allowLoadMore.current = true;
        console.log(
          "Load more enabled after timer, receiptList.length:",
          receiptList.length,
        );
      }, 1000);
    }

    return () => {
      if (loadMoreTimer.current) {
        clearTimeout(loadMoreTimer.current);
      }
    };
  }, [receiptList.length]);

  // Reset load more flag when filter or sort changes
  useEffect(() => {
    console.log("Reset load more flag due to filter/sort change");
    allowLoadMore.current = false;

    // Clear any existing timer
    if (loadMoreTimer.current) {
      clearTimeout(loadMoreTimer.current);
    }
  }, [
    activeTab,
    pagination.config.filter.filterType,
    pagination.config.sort.sortKey,
    pagination.config.sort.sortOrder,
  ]);

  // Handle load more for infinite scroll
  const handleLoadMore = () => {
    console.log("=== handleLoadMore called ===");
    console.log("allowLoadMore.current:", allowLoadMore.current);
    console.log("pagination.hasNextPage:", pagination.hasNextPage);
    console.log("pagination.isLoadingMore:", pagination.isLoadingMore);
    console.log("receiptList.length:", receiptList.length);
    console.log("pagination.allData.length:", pagination.allData.length);
    console.log("pagination.config:", pagination.config);

    // Only load more if allowed and other conditions are met
    if (
      allowLoadMore.current &&
      pagination.hasNextPage &&
      !pagination.isLoadingMore &&
      receiptList.length > 0
    ) {
      console.log("✅ All conditions met, calling pagination.loadMore()");
      pagination.loadMore();
    } else {
      console.log("❌ Load more blocked by conditions:");
      console.log("  - allowLoadMore.current:", allowLoadMore.current);
      console.log("  - pagination.hasNextPage:", pagination.hasNextPage);
      console.log("  - !pagination.isLoadingMore:", !pagination.isLoadingMore);
      console.log("  - receiptList.length > 0:", receiptList.length > 0);
    }
  };

  // Wrapper to log when TableVirtuoso calls endReached
  const handleEndReached = () => {
    console.log("🎯 TableVirtuoso endReached triggered");
    handleLoadMore();
  };

  const selectRecord = (record: Receipt) => {
    handleUnSelectRecord(); // 選択を初期化
    handleSelectRecord(record);
  };

  const onClickRow = (receipt: Receipt) => {
    const { seikyuYm } = getValues();
    const { ptId, ptNum, sinYm, hokenId, hokenKbn, seikyuKbn } = receipt;

    const query = {
      seikyuYm: seikyuYm?.format("YYYYMM").toString() ?? "",
      ptId: ptId ?? "0",
      ptNum: ptNum ?? "0",
      sinDate: (sinYm ?? 0) * 100 + 1,
      sinYm: sinYm ?? 0,
      hokenId: hokenId ?? 0,
      hokenKbn: hokenKbn ?? 0,
      seikyuKbn: seikyuKbn ?? 0,
    };

    router.push({
      pathname: "/setting/receipt/check",
      query,
    });
  };

  // Handle sorting
  const handleSort = (sortField: string, sortOrder: SortOrder | null) => {
    let sortKey = 0; // Default to patient number
    if (sortField === "ptNum") sortKey = 0;
    else if (sortField === "kanaName") sortKey = 1;
    else if (sortField === "hokenKbn") sortKey = 2;
    else if (sortField === "lastVisitDate") sortKey = 3;

    const sortOrderBool = sortOrder === SortOrder.Ascend;
    pagination.updateSort(sortKey, sortOrderBool);
    pagination.reset();
    pagination.loadFirstPage();
  };

  // Define columns for InfiniteScrollTable
  const columns: TableColumn[] = [
    {
      title: "請求",
      dataIndex: "seikyuKbn",
      width: "70px",
    },
    {
      title: "診療年月",
      dataIndex: "sinYm",
      width: "100px",
    },
    {
      title: "紙",
      dataIndex: "isPaperRece",
      width: "50px",
    },
    {
      title: "確認",
      dataIndex: "statusKbn",
      width: "80px",
    },
    {
      title: "患者番号",
      dataIndex: "ptNum",
      width: "100px",
      sortable: true,
    },
    {
      title: "氏名",
      dataIndex: "kanaName",
      width: "200px",
      sortable: true,
    },
    {
      title: "レセプト種別",
      dataIndex: "hokenKbn",
      width: "170px",
      sortable: true,
    },
    {
      title: "診療情報",
      dataIndex: "kaName",
      width: "170px",
    },
    {
      title: "最終来院日",
      dataIndex: "lastVisitDate",
      width: "110px",
      sortable: true,
    },
    {
      title: "保険者番号",
      dataIndex: "hokensyaNo",
      width: "100px",
    },
    {
      title: "点数",
      dataIndex: "tensu",
      width: "80px",
    },
    {
      title: "実日数",
      dataIndex: "hokenNissu",
      width: "70px",
    },
    {
      title: "コメント",
      dataIndex: "isReceCmtExist",
      width: "100px",
    },
    {
      title: "症状詳記",
      dataIndex: "isSyoukiInfExist",
      width: "100px",
    },
    {
      title: "傷病経過",
      dataIndex: "isSyobyoKeikaExist",
      width: "100px",
    },
    {
      title: "再請求コメント",
      dataIndex: "receSeikyuCmt",
      width: "160px",
    },
    {
      title: "",
      dataIndex: "actions",
      width: "40px",
    },
  ];

  // Custom table row component
  const CustomTableRow = ({
    item,
    ...props
  }: {
    item: Receipt;
    [key: string]: unknown;
  }) => {
    const renderCellContent = (
      column: TableColumn,
      receipt: Receipt,
    ): React.ReactNode => {
      switch (column.dataIndex) {
        case "seikyuKbn":
          if (receipt.seikyuKbn === 1) return "月遅れ";
          if (receipt.seikyuKbn === 2) return "返戻";
          if (receipt.seikyuKbn === 3) return "オ返戻";
          return "";

        case "sinYm":
          if (!receipt.sinYm || String(receipt.sinYm).length !== 6) {
            return String(receipt.sinYm);
          }
          return convert6digitToYYYYMMwithSlash(receipt.sinYm);

        case "isPaperRece":
          if (
            receipt.isPaperRece === 1 ||
            receipt.seikyuKbn === 2 ||
            receipt.hokenKbn === 0 ||
            receipt.hokenKbn === 14
          ) {
            return "○";
          }
          return "";

        case "statusKbn":
          if (receipt.statusKbn === 0) return <ConfirmBadge>未</ConfirmBadge>;
          if (receipt.statusKbn === 1)
            return <ConfirmBadge>シス保留</ConfirmBadge>;
          if (receipt.statusKbn === 2) return <ConfirmBadge>保留</ConfirmBadge>;
          if (receipt.statusKbn === 3)
            return <ConfirmBadge>保留2</ConfirmBadge>;
          if (receipt.statusKbn === 4)
            return <ConfirmBadge>保留3</ConfirmBadge>;
          if (receipt.statusKbn === 8) return <ConfirmBadge>仮</ConfirmBadge>;
          if (receipt.statusKbn === 9)
            return <ConfirmBadge $isDone>済</ConfirmBadge>;
          return "";

        case "ptNum":
          return (
            <div>
              {receipt.isPtTest && (
                <TestPatientBadge>テスト患者</TestPatientBadge>
              )}
              {receipt.ptNum ?? ""}
            </div>
          );

        case "kanaName":
          return (
            <div>
              <SubName>{receipt.kanaName}</SubName>
              <PatientInfo>
                <Name>{receipt.name}</Name>
                <GenderBagde $gender={receipt.sex ?? 0}>
                  <p>
                    {receipt.sex === 1 ? "M" : receipt.sex === 2 ? "F" : "　"}
                  </p>
                </GenderBagde>
                <p style={{ flexShrink: 0 }}>{receipt.age}</p>
              </PatientInfo>
            </div>
          );

        case "hokenKbn":
          if (receipt.hokenKbn === 0) {
            if (receipt.receSbt?.length === 4) {
              let result = "";
              if (receipt.receSbt[0] === "8") {
                result = "自費";
              } else if (receipt.receSbt[0] === "9") {
                result = "自費レセ";
              }
              const kohiCount = Number(receipt.receSbt[2]);
              if (kohiCount > 0) {
                const prefix = getKohiCountName(kohiCount);
                return result + prefix;
              }
            }
            return "";
          }
          if (receipt.hokenKbn === 1) {
            return typeof receipt.receSbt === "string" &&
              receipt.receSbt in shaHoDict
              ? shaHoDict[receipt.receSbt]
              : "";
          }
          if (receipt.hokenKbn === 2) {
            return typeof receipt.receSbt === "string" &&
              receipt.receSbt in kokuHoDict
              ? kokuHoDict[receipt.receSbt]
              : "";
          }
          if (receipt.hokenKbn === 11) return "労災(短期給付)";
          if (receipt.hokenKbn === 12) return "労災(傷病年金)";
          if (receipt.hokenKbn === 13) return "アフターケア";
          if (receipt.hokenKbn === 14) return "自賠責";
          return "";

        case "kaName":
          return `${receipt.kaName ? `${receipt.kaName}：` : ""}${receipt.sName ?? ""}`;

        case "lastVisitDate":
          return receipt.lastVisitDateDisplay;

        case "hokensyaNo":
          return receipt.hokensyaNo;

        case "tensu":
          return <div style={{ textAlign: "right" }}>{receipt.tensu}</div>;

        case "hokenNissu":
          if (String(receipt.hokenSbtCd).startsWith("5")) {
            return receipt.kohi1Nissu ?? "";
          }
          return receipt.hokenNissu ?? "";

        case "isReceCmtExist":
          return receipt.isReceCmtExist === 1 ? "◯" : "";

        case "isSyoukiInfExist":
          return receipt.isSyoukiInfExist === 1 ? "◯" : "";

        case "isSyobyoKeikaExist":
          return receipt.isSyobyoKeikaExist === 1 ? "◯" : "";

        case "receSeikyuCmt":
          return receipt.receSeikyuCmt ?? "";

        case "actions":
          return (
            <DropdownMenu
              trigger={["click"]}
              menu={{
                items: [
                  {
                    key: "PREVIEW",
                    label: <PreviewMenu receipt={receipt} />,
                  },
                  {
                    key: "CANCEL",
                    label: <CancelText>請求取り消し</CancelText>,
                    onClick: () => {
                      selectRecord(receipt);
                      handleOpenModal("CANCEL_CLAIM");
                    },
                  },
                ],
              }}
            >
              <IconButton varient="icon-only" icon={<SvgIconMore />} />
            </DropdownMenu>
          );

        default:
          return String(
            (receipt as Record<string, unknown>)[column.dataIndex] ?? "",
          );
      }
    };

    return (
      <tr
        {...props}
        onClick={() => onClickRow(item)}
        style={{ cursor: "pointer" }}
      >
        {columns.map((column, index) => (
          <TableDataCell
            key={index}
            align={
              column.dataIndex === "seikyuKbn" ||
              column.dataIndex === "sinYm" ||
              column.dataIndex === "isPaperRece" ||
              column.dataIndex === "statusKbn" ||
              column.dataIndex === "ptNum" ||
              column.dataIndex === "lastVisitDate" ||
              column.dataIndex === "hokensyaNo" ||
              column.dataIndex === "hokenNissu" ||
              column.dataIndex === "isReceCmtExist" ||
              column.dataIndex === "isSyoukiInfExist" ||
              column.dataIndex === "isSyobyoKeikaExist" ||
              column.dataIndex === "receSeikyuCmt" ||
              column.dataIndex === "actions"
                ? "center"
                : "left"
            }
            bordered
          >
            {renderCellContent(column, item)}
          </TableDataCell>
        ))}
      </tr>
    );
  };

  // Handle tab change - update filter and reload data
  const handleTabChange = async (key: string) => {
    if (
      key === "ALL" ||
      key === "SHAHO" ||
      key === "KOKUHO" ||
      key === "OTHERS"
    ) {
      setActiveTab(key);

      // Map tab to filterType
      let filterType = 0; // ALL
      if (key === "SHAHO") filterType = 1;
      else if (key === "KOKUHO") filterType = 2;
      else if (key === "OTHERS") filterType = 3;

      // Update filter and reload data
      pagination.updateFilter(filterType);
      pagination.reset();
      await pagination.loadFirstPage();
    }
  };

  // Use receiptList directly since filtering is now server-side
  const filteredList = receiptList;

  const totalPoint = filteredList.reduce(
    (total, item) => total + (item.tensu || 0),
    0,
  );

  return (
    <>
      <ListHeader>
        <HeaderTopWrapper>
          <Title>レセプト</Title>
          <DropdownMenu
            trigger={["click"]}
            menu={{
              items: [
                {
                  key: "PRINT",
                  label: "レセプトチェック一覧印刷",
                  onClick: () => handleOpenModal("PRINT_LIST"),
                },
                {
                  key: "CSV",
                  label: "CSV保存",
                  onClick: () => handleOpenModal("EXPORT_CSV"),
                },
              ],
            }}
          >
            <IconButton varient="square" icon={<SvgIconMore />} />
          </DropdownMenu>
        </HeaderTopWrapper>

        <StyledLabel label="処理年月" />
        <ActionWrapper>
          <DatePickerWrapper>
            <Controller
              name="seikyuYm"
              control={control}
              render={({ field }) => (
                <DatePicker
                  {...field}
                  picker="month"
                  suffixIcon={<SvgIconCalendar />}
                  allowClear={false}
                  format="YYYY/MM"
                  onChange={(e) => {
                    field.onChange(e);
                    search(getValues());
                  }}
                />
              )}
            />

            <StyledButton
              varient="standard-sr"
              onClick={() => handleOpenModal("SEARCH_CRITERIA")}
            >
              条件設定
            </StyledButton>
          </DatePickerWrapper>
          <ActionButtonsWrapper>
            <ActionButton
              varient="secondary"
              onClick={() => handleOpenModal("RECALCULATION")}
            >
              集計(再計算)
            </ActionButton>
            <ActionButton
              varient="secondary"
              onClick={() => handleOpenModal("CREATE_DATA")}
            >
              データ作成
            </ActionButton>
            <ActionButton
              varient="standard"
              onClick={() => handleOpenModal("PRINT")}
            >
              印刷
            </ActionButton>
          </ActionButtonsWrapper>
        </ActionWrapper>
        <HeaderBottomWrapper>
          <StyledTabs
            activeKey={activeTab}
            items={[
              {
                key: "ALL",
                label: "すべて",
              },
              {
                key: "SHAHO",
                label: "社保",
              },
              {
                key: "KOKUHO",
                label: "国保",
              },
              {
                key: "OTHERS",
                label: "その他",
              },
            ]}
            onTabClick={handleTabChange}
            tabBarExtraContent={
              <ButtonInTab
                varient="standard-sr"
                onClick={() => handleOpenModal("PENDING_LIST")}
              >
                請求保留一覧
              </ButtonInTab>
            }
          />
          <ListInfoWrapper>
            <p>{filteredList.length}件</p>
            <p>表示点数{totalPoint.toLocaleString()}点</p>
          </ListInfoWrapper>
        </HeaderBottomWrapper>
      </ListHeader>

      <TableContainer>
        <InfiniteScrollTable
          data={filteredList}
          columns={columns}
          onLoadMore={handleEndReached}
          isLoadingMore={pagination.isLoadingMore}
          onSort={handleSort}
          sortField={
            pagination.config.sort.sortKey === 0
              ? "ptNum"
              : pagination.config.sort.sortKey === 1
                ? "kanaName"
                : pagination.config.sort.sortKey === 2
                  ? "hokenKbn"
                  : pagination.config.sort.sortKey === 3
                    ? "lastVisitDate"
                    : undefined
          }
          sortOrder={
            pagination.config.sort.sortOrder
              ? SortOrder.Ascend
              : SortOrder.Descend
          }
          onRowClick={onClickRow}
          customTableRow={CustomTableRow}
          emptyPlaceholder="検索結果がございません。"
          style={{ height: "calc(100vh - 310px)" }}
        />
      </TableContainer>

      <SearchCriteriaModal />
      {modal.recalculationOpen && <RecalculationModal />}
      {modal.createDataOpen && <DataCreationModal />}
      {modal.printOpen && <PrintModal />}
      {modal.pendingListOpen && (
        <SelectRecordProvider>
          <PendingListModal />
        </SelectRecordProvider>
      )}
      {modal.printListOpen && <PrintListModal receiptList={filteredList} />}
      {modal.exportCsvOpen && <ExportCsvListModal />}
      {modal.cancelClaimOpen && <CancelClaimModal />}
    </>
  );
};
